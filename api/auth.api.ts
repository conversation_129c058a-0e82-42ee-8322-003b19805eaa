import { OtpPurpose } from "@/contexts/AuthContext";
import api from "./axios";

/**
 * Interface for API responses
 */
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

export interface IRegisterResponse {
  id: number;
  name: string;
  phone: string;
  account_locked_reason: string | null;
  api_token: string;
  fcm_token: string | null;
  lat: string | null;
  lng: string | null;
  is_active: number;
  is_phone_confirmed: number;
  photo: string | null;
  referral_code: string | null;
  updated_at: string;
}

/**
 * Validate password
 * @param password Password to validate
 * @returns Object with validation result and error message
 */
export const validatePassword = (
  password: string
): { isValid: boolean; error?: string } => {
  if (!password) {
    return { isValid: false, error: "Mật khẩu không được để trống" };
  }

  if (password.length !== 6) {
    return { isValid: false, error: "<PERSON><PERSON>t khẩu phải có đúng 6 ký tự" };
  }

  if (!/^\d{6}$/.test(password)) {
    return { isValid: false, error: "Mật khẩu phải chỉ gồm 6 chữ số" };
  }

  return { isValid: true };
};

/**
 * Check if a phone number is already registered
 * @param phone Phone number to check
 * @returns Promise with boolean indicating if the phone is registered
 */
export const checkPhoneRegistered = async (
  phone: string
): Promise<{ is_registered: boolean }> => {
  try {
    const response = await api.get(`/api/v1/auth/check-phone/${phone}`);
    return response.data;
  } catch (error) {
    console.error("Error checking phone:", error);
    throw error;
  }
};

/**
 * Sign in with phone and password
 * @param phone User's phone number
 * @param password User's password
 * @returns Promise with user data and tokens
 */
export const signIn = async (
  phone: string,
  password: string
): Promise<{
  user: IRegisterResponse;
  apiToken: string;
}> => {
  try {
    const response = await api.post("/api/v1/auth/sign-in", {
      phone,
      password,
    });
    return response.data;
  } catch (error) {
    console.error("Error signing in:", error);
    throw error;
  }
};

// {"account_locked_reason": null, "api_token": "OR9zSZ1CVWldNXBIRIYCHusG7QaPz51BrJfiRzGsdV198Xpa1M", "created_at": "2025-05-13T06:17:37.341Z", "fcm_token": null, "id": 4, "is_active": 1, "is_phone_confirmed": 0, "lat": null, "lng": null, "name": "James Nguyen", "password": "$2b$10$5PqgZa5I5R4e9/0AxEgtGOKsXGWOUHrAoOJ7ee2/O0zQZDYsrH6QC", "phone": "**********", "photo": null, "referral_code": "EP4Z4A", "updated_at": "2025-05-13T06:17:37.341Z"}
export interface IUser {
  id: number;
  name: string;
  phone: string;
  account_locked_reason: string | null;
  api_token: string;
  fcm_token: string | null;
  lat: string | null;
  lng: string | null;
  is_active: number;
  is_phone_confirmed: number;
  photo: string | null;
  referral_code: string | null;
  updated_at: string;
}

/**
 * Register a new user
 * @param userData User registration data
 * @returns Promise with user data and tokens
 */
export const register = async (userData: {
  name: string;
  phone: string;
  password: string;
  referral_code?: string;
  fcm_token?: string;
}): Promise<IUser> => {
  try {
    const response = await api.post<IUser>("/api/v1/auth/register", userData);
    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Verify a referral code
 * @param code Referral code to verify
 * @returns Promise with validity status and referrer info
 */
export const verifyReferralCode = async (
  code: string
): Promise<{
  valid: boolean;
  referrer?: {
    id: number;
    name: string;
  };
}> => {
  try {
    const response = await api.get(`/api/v1/auth/verify-referral/${code}`);
    return response.data;
  } catch (error) {
    console.error("Error verifying referral code:", error);
    return { valid: false };
  }
};

/**
 * Resend OTP to a phone number
 * @param phone Phone number to send OTP to
 * @returns Promise with success status
 */
export const resendOTP = async (
  phone: string,
  type: OtpPurpose
): Promise<{
  success: boolean;
  message?: string;
}> => {
  try {
    console.log("Resending OTP to:", { phone, type });
    const response = await api.post("/api/v1/auth/resend-otp", {
      phone,
      type,
    });
    return response.data;
  } catch (error) {
    console.error("Error resending OTP:", error);
    throw error;
  }
};

/**
 * Verify OTP for registration
 * @param phone Phone number
 * @param otp OTP code
 * @returns Promise with verification result
 */
export const verifyOTP = async (
  phone: string,
  otp: string,
  type: OtpPurpose
): Promise<{
  success: boolean;
  message?: string;
}> => {
  try {
    const response = await api.post("/api/v1/auth/verify-otp", {
      phone,
      otp,
      type,
    });
    return response.data;
  } catch (error) {
    console.error("Error verifying OTP:", error);
    throw error;
  }
};
