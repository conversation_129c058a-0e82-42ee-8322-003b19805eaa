import axios from "axios";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho API Error
interface ApiError extends Error {
  response?: any;
  statusCode?: number;
  data?: any;
  request?: any;
}

// Tạo instance axios global với baseURL
const api = axios.create({
  baseURL: "https://api-dev.saygo.vn",
  // baseURL: "http://localhost:3588",
  timeout: 10000, // Timeout sau 10 giây
  headers: {
    "Content-Type": "application/json",
    Accept: "application/json",
  },
});

// Thêm interceptor để xử lý request
api.interceptors.request.use(
  async (config) => {
    try {
      // Lấy token từ AsyncStorage
      const token = await AsyncStorage.getItem("@apiToken");

      if (token) {
        config.headers.Authorization = `Bearer ${token}`;
      }
    } catch (error) {
      console.error("Error getting token:", error);
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Thêm interceptor để xử lý response
api.interceptors.response.use(
  (response) => {
    return response;
  },
  async (error) => {
    // Xử lý lỗi chung ở đây
    if (error.response) {
      // Tạo đối tượng lỗi với message từ response
      const errorData = error.response.data;
      const errorMessage = errorData?.message || "Đã xảy ra lỗi";

      if (error.response.status === 401) {
        try {
          await AsyncStorage.removeItem("@user");
          await AsyncStorage.removeItem("@apiToken");

          router.replace("/login-phone");
        } catch (e) {
          console.error("Error during logout:", e);
        }
      }

      // Tạo đối tượng lỗi mới với message rõ ràng
      const enhancedError = new Error(errorMessage) as ApiError;
      enhancedError.response = error.response;
      enhancedError.statusCode = error.response.status;
      enhancedError.data = errorData;

      return Promise.reject(enhancedError);
    } else if (error.request) {
      // Không nhận được response
      const networkError = new Error(
        "Không thể kết nối đến máy chủ"
      ) as ApiError;
      networkError.request = error.request;
      return Promise.reject(networkError);
    } else {
      // Lỗi khi setup request
      console.error("API Setup Error:", error.message);
      return Promise.reject(
        new Error("Lỗi kết nối: " + error.message) as ApiError
      );
    }
  }
);

export default api;
