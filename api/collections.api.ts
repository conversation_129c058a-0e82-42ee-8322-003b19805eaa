import api from "./axios";

/**
 * Interface for a collection item
 */
export interface CollectionItem {
  id: string;
  name: string;
  image: string;
  route: string;
}

/**
 * Interface for API response
 */
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

/**
 * Get all collections
 * @returns Promise with all collections
 */
export const getCollections = async (): Promise<CollectionItem[]> => {
  try {
    const response = await api.get("/api/v1/collections");
    return response.data;
  } catch (error) {
    console.error("Error fetching collections:", error);
    throw error;
  }
};

/**
 * Get collections by type
 * @param type The collection type
 * @returns Promise with collections of the specified type
 */
export const getCollectionsByType = async (type: string): Promise<ApiResponse<CollectionItem[]>> => {
  try {
    const response = await api.get(`/api/v1/collections?type=${type}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching collections of type ${type}:`, error);
    throw error;
  }
};
