import GoongConstants from "@/constants/GoongConstants";
import polyline from "@mapbox/polyline";

export interface DirectionResponse {
  routes: {
    legs: {
      distance: {
        text: string;
        value: number;
      };
      duration: {
        text: string;
        value: number;
      };
      steps: any[];
    }[];
    overview_polyline: {
      points: string;
    };
    summary: string;
    distance: {
      text: string;
      value: number;
    };
    duration: {
      text: string;
      value: number;
    };
  }[];
  status: string;
}

export interface RouteInfo {
  distance: {
    text: string;
    value: number;
  };
  duration: {
    text: string;
    value: number;
  };
  polylineCoordinates: [number, number][];
}

/**
 * Get directions between two points using Goong API
 * @param origin Origin coordinates [latitude, longitude]
 * @param destination Destination coordinates [latitude, longitude]
 * @param vehicle Vehicle type (car, bike, taxi, truck)
 * @returns Promise with route information
 */
export const getDirections = async (
  origin: [number, number],
  destination: [number, number],
  vehicle: "car" | "bike" | "taxi" | "truck" = "car"
): Promise<RouteInfo | null> => {
  try {
    const originStr = `${origin[0]},${origin[1]}`;
    const destinationStr = `${destination[0]},${destination[1]}`;

    const response = await fetch(
      `https://rsapi.goong.io/Direction?origin=${originStr}&destination=${destinationStr}&vehicle=${vehicle}&api_key=${GoongConstants.API_KEY}`
    );

    const data: DirectionResponse = await response.json();

    if (data.routes.length === 0) {
      console.error("Direction API error:", data);
      return null;
    }

    const route = data.routes[0];

    const polylineCoordinates = polyline
      .decode(route.overview_polyline.points)
      .map(([lat, lng]: [number, number]) => [lng, lat] as [number, number]);

    return {
      distance: route.legs[0].distance,
      duration: route.legs[0].duration,
      polylineCoordinates,
    };
  } catch (error) {
    console.error("Error getting directions:", error);
    return null;
  }
};
