import api from "./axios";
import GoongConstants from "@/constants/GoongConstants";

export interface GeocodingResult {
  lat: number;
  lng: number;
  formattedAddress: string;
}

/**
 * Get coordinates from place_id using Goong API
 * @param placeId The place_id to get coordinates for
 * @returns Promise with the coordinates and formatted address
 */
export const getCoordinatesFromPlaceId = async (
  placeId: string
): Promise<GeocodingResult | null> => {
  try {
    // Use Goong API directly since we need to use the API key
    const response = await fetch(
      `https://rsapi.goong.io/Place/Detail?place_id=${placeId}&api_key=${GoongConstants.API_KEY}`
    );
    
    const data = await response.json();
    
    if (data && data.result && data.result.geometry && data.result.geometry.location) {
      return {
        lat: data.result.geometry.location.lat,
        lng: data.result.geometry.location.lng,
        formattedAddress: data.result.formatted_address || "",
      };
    }
    
    return null;
  } catch (error) {
    console.error("Error getting coordinates from place_id:", error);
    return null;
  }
};
