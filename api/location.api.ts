import api from "./axios";

export interface LocationSuggestion {
  description: string;
  place_id: string;
}

export const searchLocations = async (
  input: string,
  limit: number = 5
): Promise<LocationSuggestion[]> => {
  try {
    const response = await api.get("/api/v1/locations/autocomplete", {
      params: {
        input,
        limit,
      },
    });

    return response.data || [];
  } catch (error) {
    console.error("Error searching locations:", error);
    return [];
  }
};
