import api from "./axios";

/**
 * Interface for payment methods
 */
export interface PaymentMethod {
  id: number;
  name: string;
  code: string;
  icon: string;
}

/**
 * Interface for API response
 */
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

/**
 * Get all payment methods
 * @returns Promise with all payment methods
 */
export const getPaymentMethods = async (): Promise<PaymentMethod[]> => {
  try {
    const response = await api.get("/api/v1/payment-methods");
    return response.data || [];
  } catch (error) {
    console.error("Error fetching payment methods:", error);
    // Return default payment methods as fallback
    return [
      {
        id: 1,
        name: "<PERSON>i<PERSON>n mặt",
        code: "CASH",
        icon: "https://villshipdev.sgp1.digitaloceanspaces.com/baoloc/frames/1f65e220b-2054-4854-a494-21abbc4b97fc.png"
      },
      {
        id: 2,
        name: "QR Code",
        code: "QRCODE",
        icon: "https://villshipdev.sgp1.digitaloceanspaces.com/baoloc/frames/1f65e220b-2054-4854-a494-21abbc4b97fc.png"
      },
      {
        id: 3,
        name: "ZaloPay",
        code: "ZALOPAY",
        icon: "https://villshipdev.sgp1.digitaloceanspaces.com/baoloc/frames/1f65e220b-2054-4854-a494-21abbc4b97fc.png"
      },
      {
        id: 4,
        name: "MoMo",
        code: "MOMO",
        icon: "https://villshipdev.sgp1.digitaloceanspaces.com/baoloc/frames/1f65e220b-2054-4854-a494-21abbc4b97fc.png"
      }
    ];
  }
};
