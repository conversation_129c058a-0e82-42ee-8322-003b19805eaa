import api from "./axios";

/**
 * Interface for a setting object (database format)
 */
export interface Setting {
  id?: number;
  setting_key?: string;
  setting_value?: Record<string, any>;
  is_client?: number;
  is_driver?: number;
  created_at?: string;
  updated_at?: string;
  // API response format
  key?: string;
  value?: Record<string, any>;
}

/**
 * Interface for API response
 */
export interface ApiResponse<T> {
  success: boolean;
  message: string;
  data: T;
}

/**
 * Get all settings
 * @returns Promise with all settings
 */
export const getAllSettings = async (): Promise<ApiResponse<Setting[]>> => {
  try {
    const response = await api.get("/api/v1/settings");
    return response.data;
  } catch (error) {
    console.error("Error fetching all settings:", error);
    throw error;
  }
};

/**
 * Get a specific setting by key
 * @param key The setting key
 * @returns Promise with the setting value
 */
export const getSettingByKey = async (
  key: string
): Promise<ApiResponse<Record<string, any>>> => {
  try {
    const response = await api.get(`/api/v1/settings/${key}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching setting ${key}:`, error);
    throw error;
  }
};
