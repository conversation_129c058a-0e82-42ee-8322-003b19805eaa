import { ApiResponse } from "@/store/slices/fineSlice";
import api from "./axios";

// Gọi API thực tế để tra cứu phạt nguội
export const checkTrafficViolations = async (
  licensePlate: string
): Promise<ApiResponse> => {
  try {
    const formattedPlate = licensePlate.replace(/\s+/g, "-").toUpperCase();

    // Gọi API thực tế - sử dụng JSON thay vì FormData
    const response = await api.post(
      "/api/v1/traffic-violations/check",
      { licensePlate: formattedPlate }, // Gửi dữ liệu dưới dạng JSON
      {
        timeout: 15000, // Timeout sau 15 giây
      }
    );

    // API trả về dữ liệu đã được cấu trúc
    return response.data;
  } catch (error) {
    console.error("Error checking traffic violations:", error);

    return {
      success: false,
      message: "<PERSON><PERSON> lỗi xảy ra khi kết nối đến máy chủ. Vui lòng thử lại sau.",
      totalViolations: 0,
      pendingViolations: 0,
      resolvedViolations: 0,
      violations: [],
    };
  }
};
