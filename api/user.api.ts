import api from "./axios";

/**
 * Interface for user profile update data
 */
export interface UpdateUserProfileData {
  name: string;
  photo?: string;
}

/**
 * Interface for API response
 */
export interface ApiResponse<T> {
  success: boolean;
  message?: string;
  data: T;
}

/**
 * Update user profile
 * @param profileData User profile data to update
 * @returns Promise with updated user data
 */
export const updateUserProfile = async (
  profileData: UpdateUserProfileData
): Promise<ApiResponse<any>> => {
  try {
    const response = await api.post("/api/v1/users/profile", profileData);
    return response.data;
  } catch (error) {
    console.error("Error updating user profile:", error);
    throw error;
  }
};

/**
 * Get user profile
 * @returns Promise with user profile data
 */
export const getUserProfile = async (): Promise<ApiResponse<any>> => {
  try {
    const response = await api.get("/api/v1/users/profile");
    return response.data;
  } catch (error) {
    console.error("Error getting user profile:", error);
    throw error;
  }
};

/**
 * Get presigned URL for photo upload
 * @returns Promise with presigned URL data
 */
export const getPhotoUploadUrl = async (): Promise<
  {
    url: string;
    key: string;
  }[]
> => {
  try {
    const response = await api.get("/api/v1/users/photo/presigned-url");
    return response.data;
  } catch (error) {
    console.error("Error getting photo upload URL:", error);
    throw error;
  }
};
