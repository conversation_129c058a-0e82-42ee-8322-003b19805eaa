import { Colors } from "@/constants/Colors";
import { useAuth } from "@/contexts/AuthContext";
import { useAppSelector } from "@/store/hooks";
import { r2Image } from "@/utils/r2-image";
import { MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import React from "react";
import {
  Alert,
  Image,
  Platform,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Define extended User type to match what's in AuthContext
interface ExtendedUser {
  id: string | number;
  name: string;
  phone: string;
  photo?: string | null;
  referral_code?: string | null;
  is_phone_confirmed?: number;
  is_active?: number;
}

// Define the icon type to match MaterialIcons
type IconName = keyof typeof MaterialIcons.glyphMap;

// Define menu item interface
interface MenuItem {
  icon: IconName;
  label: string;
  isNew?: boolean;
  url?: string;
  code?: string;
}

const menuItems: MenuItem[] = [
  {
    code: "DIRECTIONS_CAR",
    icon: "directions-car",
    label: "Đ<PERSON>ng ký tài xế",
    isNew: true,
    url: "https://forms.gle/LhfdgTRGhVDG5mKM9",
  },
  {
    code: "HANDSHAKE",
    icon: "handshake",
    label: "Trở thành đối tác",
    isNew: true,
    url: "https://forms.gle/gNbaKCG92KzM7qiB6",
  },
  {
    code: "SECURITY",
    icon: "security",
    label: "Chính sách bảo mật",
  },
  {
    code: "GAVEL",
    icon: "gavel",
    label: "Điều khoản dịch vụ",
  },
];

export default function AccountScreen() {
  const { user: authUser, signOut } = useAuth();
  const insets = useSafeAreaInsets();

  const user = authUser as unknown as ExtendedUser;

  // Lấy thông tin người dùng từ Redux store
  const reduxUser = useAppSelector((state) => state.user);

  // Tính toán padding bottom để tránh bị thanh điều hướng che khuất
  // Thêm 80px để đảm bảo có đủ khoảng cách
  const bottomPadding = Platform.OS === "ios" ? insets.bottom + 80 : 80;

  // Ưu tiên sử dụng dữ liệu từ Redux nếu có
  const displayName = reduxUser.name || user?.name || "Hiep Nguyen";
  const displayPhone = reduxUser.phone || user?.phone || "+***********";

  // Lấy thêm thông tin từ AuthContext
  // const referralCode = user?.referral_code || "N/A";
  const isPhoneConfirmed = user?.is_phone_confirmed === 1;
  const isActive = user?.is_active === 1;
  const userPhoto = user?.photo || "static/users-photo/RvbodRTR04RzD2Pv.jpg";

  // Hàm xử lý sao chép mã giới thiệu
  // const handleCopyReferralCode = async () => {
  //   if (referralCode !== "N/A") {
  //     await Clipboard.setStringAsync(referralCode);
  //   }
  // };

  const handleSignOut = () => {
    Alert.alert("Đăng xuất", "Bạn có chắc chắn muốn đăng xuất?", [
      {
        text: "Hủy",
        style: "cancel",
      },
      {
        text: "Đăng xuất",
        onPress: async () => {
          await signOut();
          router.replace("/auth");
        },
        style: "destructive",
      },
    ]);
  };

  return (
    <SafeAreaView style={{ flex: 1, backgroundColor: "#FFD100" }}>
      <ScrollView
        style={styles.container}
        contentContainerStyle={{ paddingBottom: bottomPadding }}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <TouchableOpacity
            style={styles.editIcon}
            onPress={() => router.push("/edit-profile")}
            activeOpacity={0.7}
          >
            <MaterialIcons name="edit" size={20} color={Colors.primary} />
          </TouchableOpacity>
          <Image source={{ uri: r2Image(userPhoto) }} style={styles.avatar} />
          <Text style={styles.name}>{displayName}</Text>
          <Text style={styles.phone}>{displayPhone}</Text>

          <View style={styles.statusContainer}>
            {isPhoneConfirmed ? (
              <View style={[styles.statusBadge, styles.verifiedBadge]}>
                <Text style={styles.statusText}>Đã xác thực</Text>
              </View>
            ) : (
              <TouchableOpacity
                style={[styles.statusBadge, styles.unverifiedBadge]}
                onPress={() => router.push("/verify-phone")}
                activeOpacity={0.7}
              >
                <Text style={styles.statusText}>Xác thực ngay</Text>
              </TouchableOpacity>
            )}
            {!isActive && (
              <View style={[styles.statusBadge, styles.inactiveBadge]}>
                <Text style={styles.statusText}>Tài khoản bị khóa</Text>
              </View>
            )}
          </View>

          {/* {referralCode !== "N/A" && (
            <TouchableOpacity
              style={styles.referralContainer}
              onPress={handleCopyReferralCode}
              activeOpacity={0.7}
            >
              <Text style={styles.referralLabel}>Mã giới thiệu:</Text>
              <Text style={styles.referralCode}>{referralCode}</Text>
            </TouchableOpacity>
          )} */}
        </View>

        {/* Menu */}
        <View style={styles.menu}>
          {menuItems.map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.menuItem}
              onPress={() => {
                // Handle menu item clicks
                if (item.code === "SECURITY") {
                  router.push("/privacy-policy");
                } else if (item.code === "GAVEL") {
                  router.push("/terms-of-service");
                } else if (item.code === "DIRECTIONS_CAR" && item.url) {
                  router.push({
                    pathname: "/external-form",
                    params: {
                      url: encodeURIComponent(item.url),
                      title: encodeURIComponent("Đăng ký tài xế"),
                    },
                  });
                } else if (item.code === "HANDSHAKE" && item.url) {
                  router.push({
                    pathname: "/external-form",
                    params: {
                      url: encodeURIComponent(item.url),
                      title: encodeURIComponent("Trở thành đối tác"),
                    },
                  });
                } else {
                  // Handle other menu items
                  Alert.alert(
                    item.label,
                    "Tính năng này đang được phát triển",
                    [{ text: "Đóng", style: "cancel" }]
                  );
                }
              }}
            >
              <MaterialIcons
                name={item.icon}
                size={22}
                color={Colors.primary}
                style={styles.menuIcon}
              />
              <Text style={styles.menuLabel}>{item.label}</Text>
              {item.isNew && <Text style={styles.newBadge}>Mới</Text>}
            </TouchableOpacity>
          ))}

          {/* Logout button */}
          <TouchableOpacity
            style={[styles.menuItem, styles.logoutButton]}
            onPress={handleSignOut}
          >
            <MaterialIcons
              name="logout"
              size={22}
              color="#E53935"
              style={styles.menuIcon}
            />
            <Text style={[styles.menuLabel, styles.logoutText]}>Đăng xuất</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    backgroundColor: "#FFD100",
    alignItems: "center",
    paddingVertical: 10,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    position: "relative",
  },
  editIcon: {
    position: "absolute",
    right: 20,
    backgroundColor: "#fff",
    borderRadius: 24,
    padding: 6,
    zIndex: 1,
    elevation: 3,
    borderWidth: 1,
    borderColor: "rgba(0,0,0,0.05)",
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#fff",
    marginBottom: 10,
    padding: 2,
  },
  name: {
    fontSize: 20,
    fontWeight: "bold",
  },
  phone: {
    color: "#333",
    marginTop: 5,
    fontSize: 14,
  },
  statusContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "center",
    marginTop: 4,
    gap: 8,
  },
  memberBadge: {
    backgroundColor: "#884A39",
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  memberText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 12,
  },
  statusBadge: {
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  verifiedBadge: {
    backgroundColor: "#4CAF50",
  },
  unverifiedBadge: {
    backgroundColor: "#FF9800",
  },
  inactiveBadge: {
    backgroundColor: "#F44336",
  },
  statusText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 12,
  },
  referralContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginTop: 12,
    backgroundColor: "rgba(0, 0, 0, 0.28)",
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  referralLabel: {
    fontSize: 12,
    color: "#444",
    marginRight: 4,
  },
  copyHint: {
    fontSize: 10,
    color: "#444",
    fontStyle: "italic",
  },
  menu: {
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  menuItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 18,
    position: "relative",
    paddingVertical: 8,
  },
  menuIcon: {
    marginRight: 12,
  },
  menuLabel: {
    fontSize: 16,
    flex: 1,
  },
  newBadge: {
    backgroundColor: "red",
    color: "#fff",
    fontSize: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
  },
  logoutButton: {
    marginTop: 20,
    borderTopWidth: 1,
    borderTopColor: "#f0f0f0",
    paddingTop: 20,
  },
  logoutText: {
    color: "#E53935",
    fontWeight: "bold",
  },
});
