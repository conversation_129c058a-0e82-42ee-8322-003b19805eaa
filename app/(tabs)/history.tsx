import { Colors } from "@/constants/Colors";
import { FontAwesome5, Ionicons, MaterialIcons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { router } from "expo-router";
import React, { useState } from "react";
import {
  FlatList,
  SafeAreaView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from "react-native";

type IconType = "bike" | "car" | "delivery";

interface RideItem {
  id: string;
  service: string;
  from: string;
  to: string;
  date: string;
  time: string;
  price: string;
  status: string;
  iconType: IconType;
}

export default function ActivityScreen() {
  const [activeFilter, setActiveFilter] = useState("Tất cả");

  const filters = ["Tất cả", "Nội thành", "Đi tỉnh"];

  const rideHistory: RideItem[] = [
    {
      id: "1",
      service: "GrabBike",
      from: "123 Nguyễn <PERSON>, Q1",
      to: "456 <PERSON><PERSON>, Q10",
      date: "21/04/2025",
      time: "14:30",
      price: "38.000đ",
      status: "Hoàn thành",
      iconType: "bike",
    },
    {
      id: "2",
      service: "GrabCar",
      from: "20 CMT8, Q3",
      to: "80 Phan Đăng Lưu, Q.Bình Thạnh",
      date: "19/04/2025",
      time: "09:15",
      price: "85.000đ",
      status: "Hoàn thành",
      iconType: "car",
    },
    {
      id: "3",
      service: "GrabFood",
      from: "789 Lê Văn Lương, Q11",
      to: "1011 Nguyễn Trãi, Q1",
      date: "18/04/2025",
      time: "17:45",
      price: "25.000đ",
      status: "Hoàn thành",
      iconType: "delivery",
    },
    {
      id: "4",
      service: "GrabBike",
      from: "123 Nguyễn Trãi, Q1",
      to: "456 Lý Thường Kiệt, Q10",
      date: "21/04/2025",
      time: "14:30",
      price: "38.000đ",
      status: "Hoàn thành",
      iconType: "bike",
    },
  ];

  const getIconForService = (iconType: IconType) => {
    switch (iconType) {
      case "bike":
        return <FontAwesome5 name="motorcycle" size={20} color="#fff" />;
      case "car":
        return <Ionicons name="car" size={20} color="#fff" />;
      case "delivery":
        return <MaterialIcons name="delivery-dining" size={20} color="#fff" />;
      default:
        return <Ionicons name="car" size={20} color="#fff" />;
    }
  };

  const getIconBackgroundColor = (iconType: IconType): [string, string] => {
    switch (iconType) {
      case "bike":
        return ["#00c2cb", "#00a0a8"];
      case "car":
        return ["#4c66ef", "#3a56e8"];
      case "delivery":
        return ["#ff7043", "#ff5722"];
      default:
        return ["#00c2cb", "#00a0a8"];
    }
  };

  const renderEmptyState = () => {
    return (
      <View style={styles.emptyContainer}>
        {/* <Image
          source={require('@/assets/images/empty-state.png')}
          style={styles.emptyImage}
          resizeMode="contain"
        /> */}
        <Text style={styles.emptyTitle}>Chưa có hoạt động nào</Text>
        <Text style={styles.emptyText}>
          Các hoạt động của bạn sẽ xuất hiện ở đây sau khi bạn sử dụng dịch vụ
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Lịch sử hoạt động</Text>
      </View>
      {/* Filters */}
      <View style={styles.filterContainer}>
        {filters.map((filter) => (
          <TouchableOpacity
            key={filter}
            style={[
              styles.filterButton,
              activeFilter === filter && styles.activeFilterButton,
            ]}
            onPress={() => setActiveFilter(filter)}
          >
            <Text
              style={[
                styles.filterText,
                activeFilter === filter && styles.activeFilterText,
              ]}
            >
              {filter}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <FlatList
        data={rideHistory}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.rideCard}
            onPress={() =>
              router.push({
                pathname: "/ride-detail",
                params: {
                  id: item.id,
                  service: item.service,
                  from: item.from,
                  to: item.to,
                  date: item.date,
                  time: item.time,
                  price: item.price,
                  status: item.status,
                },
              })
            }
          >
            <View style={styles.rideHeader}>
              <View style={styles.serviceInfo}>
                <LinearGradient
                  colors={getIconBackgroundColor(item.iconType)}
                  style={styles.iconContainer}
                >
                  {getIconForService(item.iconType)}
                </LinearGradient>
                <View>
                  <Text style={styles.rideService}>{item.service}</Text>
                  <Text style={styles.rideDate}>
                    {item.date} • {item.time}
                  </Text>
                </View>
              </View>
              <View style={styles.statusContainer}>
                <Text style={styles.statusText}>{item.status}</Text>
              </View>
            </View>

            <View style={styles.routeContainer}>
              <View style={styles.routeIcons}>
                <View style={styles.originDot} />
                <View style={styles.routeLine} />
                <View style={styles.destinationDot} />
              </View>

              <View style={styles.routeDetails}>
                <Text style={styles.routeText} numberOfLines={1}>
                  {item.from}
                </Text>
                <Text style={styles.routeText} numberOfLines={1}>
                  {item.to}
                </Text>
              </View>
            </View>

            <View style={styles.rideFooter}>
              <Text style={styles.ridePrice}>{item.price}</Text>
              <TouchableOpacity
                style={styles.detailButton}
                onPress={() =>
                  router.push({
                    pathname: "/ride-detail",
                    params: {
                      id: item.id,
                      service: item.service,
                      from: item.from,
                      to: item.to,
                      date: item.date,
                      time: item.time,
                      price: item.price,
                      status: item.status,
                    },
                  })
                }
              >
                <Text style={styles.detailButtonText}>Chi tiết</Text>
                <Ionicons
                  name="chevron-forward"
                  size={16}
                  color={Colors.primary}
                />
              </TouchableOpacity>
            </View>
          </TouchableOpacity>
        )}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 10,
    paddingBottom: 15,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: "#222",
  },
  searchButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f5f5f5",
    justifyContent: "center",
    alignItems: "center",
  },
  filterContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 20,
    paddingHorizontal: 16,
  },
  filterButton: {
    backgroundColor: "#f5f5f5",
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: "#f0f0f0",
  },
  activeFilterButton: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  filterText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#666",
  },
  activeFilterText: {
    color: "#fff",
    fontWeight: "600",
  },

  // Empty state
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 60,
    paddingHorizontal: 30,
  },
  emptyImage: {
    width: 120,
    height: 120,
    marginBottom: 20,
    opacity: 0.8,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 10,
  },
  emptyText: {
    color: "#888",
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },

  // List container
  listContainer: {
    paddingHorizontal: 16,
    paddingBottom: 40,
  },
  rideCard: {
    backgroundColor: "#fff",
    borderRadius: 16,
    marginBottom: 16,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    borderWidth: 1,
    borderColor: "#f0f0f0",
  },
  rideHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  serviceInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  rideService: {
    fontWeight: "700",
    fontSize: 16,
    color: "#222",
  },
  rideDate: {
    fontSize: 13,
    color: "#888",
    marginTop: 2,
  },
  statusContainer: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    backgroundColor: "#E8F5E9",
    borderRadius: 12,
  },
  statusText: {
    color: "#43A047",
    fontSize: 12,
    fontWeight: "600",
  },

  // Route section
  routeContainer: {
    flexDirection: "row",
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  routeIcons: {
    width: 16,
    alignItems: "center",
    marginRight: 12,
  },
  originDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.primary,
  },
  routeLine: {
    width: 2,
    height: 30,
    backgroundColor: "#ddd",
    marginVertical: 4,
  },
  destinationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: "#ff5722",
  },
  routeDetails: {
    flex: 1,
    justifyContent: "space-between",
    height: 60,
  },
  routeText: {
    fontSize: 14,
    color: "#555",
    paddingVertical: 4,
  },

  // Footer
  rideFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: "#f5f5f5",
  },
  ridePrice: {
    fontWeight: "700",
    fontSize: 16,
    color: "#222",
  },
  detailButton: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailButtonText: {
    color: Colors.primary,
    fontWeight: "600",
    marginRight: 4,
  },
});
