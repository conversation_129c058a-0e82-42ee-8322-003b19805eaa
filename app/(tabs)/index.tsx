import { Colors } from "@/constants/Colors";
import { useAuth } from "@/contexts/AuthContext";
import { useAppSelector } from "@/store/hooks";
import { router } from "expo-router";
import React, { useState, useEffect } from "react";
import {
  ActivityIndicator,
  Alert,
  FlatList,
  Image,
  ImageBackground,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { getCollections } from "@/api/collections.api";
import { r2Image } from "@/utils/r2-image";

// Define extended User type to match what's in AuthContext
interface ExtendedUser {
  id: string | number;
  name: string;
  phone: string;
  photo?: string | null;
  referral_code?: string | null;
  is_phone_confirmed?: number;
  is_active?: number;
}

const banner = require("@/assets/images/banner.png");

const restaurants = [
  {
    name: "Bách Hỷ An",
    image: require("@/assets/images/restaurants/res1.png"),
  },
  {
    name: "<PERSON>ò Nướng Cảnh",
    image: require("@/assets/images/restaurants/res2.png"),
  },
  {
    name: "Bò Nướng Cảnh",
    image: require("@/assets/images/restaurants/res2.png"),
  },
];

const theCafes = [
  {
    name: "Bên Hiên Nhà",
    image: require("@/assets/images/the-cafes/cf1.png"),
  },
  {
    name: "Sun Coffee",
    image: require("@/assets/images/the-cafes/cf2.png"),
  },
  {
    name: "Chị Hai Ơi",
    image: require("@/assets/images/the-cafes/cf3.png"),
  },
  {
    name: "Góc 49",
    image: require("@/assets/images/the-cafes/cf4.png"),
  },
];

interface Service {
  id: string;
  name: string;
  image: any; // Can be a require() result or {uri: string}
  route: string;
}

export default function TrangChuScreen() {
  const insets = useSafeAreaInsets();
  const { user: authUser } = useAuth();
  const reduxUser = useAppSelector((state) => state.user);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(false);

  // Cast authUser to ExtendedUser to access additional properties
  const user = authUser as unknown as ExtendedUser;

  // Ưu tiên sử dụng dữ liệu từ Redux nếu có
  const displayName = reduxUser.name || user?.name || "Khách";

  // Tính toán padding bottom để tránh bị thanh điều hướng che khuất
  // Thêm 20px để đảm bảo có đủ khoảng cách
  const bottomPadding = Platform.OS === "ios" ? insets.bottom + 80 : 80;

  // Kiểm tra xem số điện thoại đã được xác thực chưa
  const isPhoneConfirmed = user?.is_phone_confirmed === 1;

  // Fetch services from API
  useEffect(() => {
    const fetchServices = async () => {
      setLoading(true);
      try {
        const response = await getCollections();
        if (response) {
          // Transform API data to match the Service interface
          const apiServices = response.map((item) => ({
            id: item.id,
            name: item.name,
            route: item.route,
            // Handle icon - if it's a URL, we'll need to load it differently
            image: r2Image(item.image),
          }));
          setServices(apiServices);
        } else {
          // If API returns success: false, set empty services array
          setServices([]);
        }
      } catch (error) {
        // Set empty services array if API call fails
        setServices([]);
      } finally {
        setLoading(false);
      }
    };

    fetchServices();
  }, []);

  const handleServicePress = (service: Service) => {
    // Kiểm tra xác thực số điện thoại trước khi cho phép truy cập dịch vụ
    if (!isPhoneConfirmed) {
      // Hiển thị thông báo yêu cầu xác thực số điện thoại
      Alert.alert(
        "Xác thực số điện thoại",
        "Bạn cần xác thực số điện thoại để sử dụng dịch vụ này.",
        [
          { text: "Hủy", style: "cancel" },
          {
            text: "Xác thực ngay",
            onPress: () => router.push("/verify-phone"),
            style: "default",
          },
        ]
      );
      return;
    }

    // Nếu đã xác thực, chuyển hướng đến màn hình dịch vụ tương ứng
    if (service.route) {
      router.push(service.route as any);
    } else {
      // Nếu không có route, hiển thị thông báo
      Alert.alert(service.name, "Dịch vụ này đang được phát triển", [
        { text: "Đóng", style: "cancel" },
      ]);
    }
  };

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <View style={styles.bannerShadowContainer}>
          <ImageBackground
            source={banner}
            style={styles.mainBanner}
            resizeMode="cover"
            imageStyle={styles.mainBannerImage}
          >
            {/* You can add content inside the banner if needed */}
          </ImageBackground>
        </View>
      </View>

      <ScrollView
        style={styles.body}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ paddingBottom: bottomPadding }}
      >
        <View style={styles.search}>
          <Text style={styles.hello}>
            Xin chào, <Text style={{ fontWeight: "bold" }}>{displayName}</Text>
          </Text>
        </View>

        {loading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" color={Colors.primary} />
            <Text style={styles.loadingText}>Đang tải dịch vụ...</Text>
          </View>
        ) : (
          <FlatList
            data={services}
            keyExtractor={(item) => item.id}
            numColumns={4}
            scrollEnabled={false}
            contentContainerStyle={{ paddingVertical: 12 }}
            renderItem={({ item }) => (
              <TouchableOpacity
                style={styles.serviceItem}
                onPress={() => handleServicePress(item)}
                activeOpacity={0.7}
              >
                <Image
                  source={{
                    uri: item.image,
                  }}
                  style={styles.serviceIcon}
                />
                <Text style={styles.serviceText}>{item.name}</Text>
              </TouchableOpacity>
            )}
          />
        )}

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Đặc Sản Chính Gốc – Gọi Là Nhớ Cả Tuổi Thơ
          </Text>
          <ScrollView
            horizontal
            showsHorizontalScrollIndicator={false}
            style={styles.bannerScroll}
          >
            {restaurants.map((restaurant, i) => (
              <View key={i} style={styles.restaurantItem}>
                <Image source={restaurant.image} style={styles.banner} />
                <Text style={styles.restaurantName}>{restaurant.name}</Text>
              </View>
            ))}
          </ScrollView>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>
            Đưa Nhau Đi Trốn… Ở Một Quán View Đẹp
          </Text>
          <FlatList
            data={theCafes}
            keyExtractor={(_, i) => i.toString()}
            numColumns={2}
            scrollEnabled={false}
            contentContainerStyle={{ paddingVertical: 12 }}
            columnWrapperStyle={{ justifyContent: "space-between" }}
            renderItem={({ item }) => (
              <TouchableOpacity style={styles.featuredItem} activeOpacity={0.8}>
                <Image source={item.image} style={styles.featuredImage} />
                <Text style={styles.cafeName}>{item.name}</Text>
              </TouchableOpacity>
            )}
          />
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: { width: "100%" },
  bannerShadowContainer: {
    width: "100%",
    borderRadius: 12,
    marginHorizontal: 0,
    marginBottom: 10,
    // iOS shadow
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
    // Android shadow
    elevation: 8,
  },
  loadingContainer: {
    padding: 20,
    alignItems: "center",
    justifyContent: "center",
    height: 150,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: Colors.primary,
  },
  mainBanner: {
    width: "100%",
    height: 220,
    overflow: "hidden",
  },
  mainBannerImage: {
    borderRadius: 12,
  },
  body: { padding: 12, flex: 1 },
  hello: { fontSize: 18 },
  subHeader: { flexDirection: "row", gap: 12, marginTop: 6 },
  subBtn: {
    backgroundColor: "#eee",
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 20,
    fontSize: 13,
  },
  search: {
    backgroundColor: "#fff",
  },
  searchInput: {
    marginTop: 16,
    padding: 12,
    backgroundColor: "#fff",
    borderRadius: 12,
    fontSize: 15,
    color: "#333",
  },
  serviceItem: {
    alignItems: "center",
    justifyContent: "center",
    width: "25%",
    marginVertical: 12,
    paddingHorizontal: 6,
  },
  serviceIcon: {
    width: 60,
    height: 60,
    marginBottom: 8,
  },
  serviceText: {
    fontSize: 12,
    textAlign: "center",
    fontWeight: "500",
    color: "#333",
  },
  bannerScroll: {
    marginVertical: 16,
  },
  banner: {
    width: 300,
    height: 120,
    borderRadius: 12,
  },
  restaurantItem: {
    marginRight: 16,
    alignItems: "center",
  },
  restaurantName: {
    marginTop: 8,
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  section: {
    marginTop: 8,
    marginBottom: 12,
  },
  sectionTitle: {
    fontWeight: "bold",
    fontSize: 16,
    marginBottom: 4,
  },
  featuredItem: {
    width: "50%",
    paddingHorizontal: 6,
    paddingVertical: 4,
    alignItems: "center",
  },
  featuredImage: {
    width: "100%",
    height: 150,
    borderRadius: 12,
    resizeMode: "cover",
  },
  cafeName: {
    marginTop: 8,
    fontSize: 14,
    fontWeight: "500",
    textAlign: "center",
    paddingHorizontal: 4,
  },
  editIcon: {
    position: "absolute",
    top: 20,
    right: 20,
    backgroundColor: "#fff",
    borderRadius: 20,
    padding: 5,
    zIndex: 1,
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "#fff",
    marginBottom: 10,
  },
  name: {
    fontSize: 20,
    fontWeight: "bold",
  },
  phone: {
    color: "#333",
    marginTop: 5,
    fontSize: 14,
  },
  memberBadge: {
    marginTop: 10,
    backgroundColor: "#884A39",
    paddingVertical: 4,
    paddingHorizontal: 12,
    borderRadius: 20,
  },
  memberText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 12,
  },
});
