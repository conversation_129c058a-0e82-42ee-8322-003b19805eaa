import React, { useState } from "react";
import {
  SafeAreaView,
  View,
  Text,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  Image,
  StatusBar,
  Dimensions,
} from "react-native";
import { Ionicons, MaterialIcons, FontAwesome } from "@expo/vector-icons";
import { Colors } from "@/constants/Colors";

const { width } = Dimensions.get("window");

interface NotificationItem {
  id: string;
  title: string;
  desc: string;
  date: string;
  time: string;
  image: string;
  isRead: boolean;
  type: "promotion" | "system" | "activity";
}

const notifications: NotificationItem[] = [
  {
    id: "1",
    title: "Ưu đãi đặc biệt dành cho bạn!",
    desc: "Nhập ngay mã SAYGO25 để tiết kiệm 25% cho chuyến đi tiếp theo của bạn, tối đa 50.000đ.",
    date: "Hôm nay",
    time: "10:45",
    image: require("@/assets/images/facebook-icon.png"),
    isRead: false,
    type: "promotion",
  },
  {
    id: "2",
    title: "Chuyến đi của bạn đã hoàn thành",
    desc: "Cảm ơn bạn đã sử dụng dịch vụ. Hãy đánh giá trải nghiệm của bạn với tài xế Nguyễn Văn A.",
    date: "Hôm nay",
    time: "08:30",
    image: require("@/assets/images/facebook-icon.png"),
    isRead: true,
    type: "activity",
  },
  {
    id: "3",
    title: "Ưu đãi khi đi SayGo đến Viettel Store!",
    desc: "Mua sắm tại Viettel Store dễ dàng hơn cùng SayGo. Giảm ngay 20.000đ cho mỗi chuyến đi.",
    date: "Hôm qua",
    time: "14:20",
    image: require("@/assets/images/facebook-icon.png"),
    isRead: true,
    type: "promotion",
  },
  {
    id: "4",
    title: "Cập nhật chính sách bảo mật",
    desc: "Chúng tôi đã cập nhật chính sách bảo mật. Vui lòng xem xét các thay đổi mới nhất.",
    date: "02/05",
    time: "09:15",
    image: require("@/assets/images/facebook-icon.png"),
    isRead: false,
    type: "system",
  },
  {
    id: "5",
    title: "Giảm 30% cho chuyến đi đầu tiên",
    desc: "Chào mừng bạn đến với SayGo! Nhận ngay ưu đãi 30% cho chuyến đi đầu tiên của bạn.",
    date: "30/04",
    time: "11:30",
    image: require("@/assets/images/facebook-icon.png"),
    isRead: true,
    type: "promotion",
  },
];

export default function NotificationScreen() {
  const [activeTab, setActiveTab] = useState("all");
  const [showOptions, setShowOptions] = useState(false);

  const tabs = [
    { id: "all", label: "Tất cả" },
    // { id: "unread", label: "Chưa đọc" },
    // { id: "promotion", label: "Khuyến mãi" },
  ];

  const filteredNotifications = notifications.filter((item) => {
    if (activeTab === "all") return true;
    if (activeTab === "unread") return !item.isRead;
    if (activeTab === "promotion") return item.type === "promotion";
    return true;
  });

  const getNotificationIcon = (type: string, isRead: boolean) => {
    const iconColor = isRead ? "#999" : Colors.primary;

    switch (type) {
      case "promotion":
        return (
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: isRead ? "#f0f0f0" : "#FFF4E5" },
            ]}
          >
            <FontAwesome
              name="gift"
              size={16}
              color={isRead ? "#999" : "#FF9500"}
            />
          </View>
        );
      case "system":
        return (
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: isRead ? "#f0f0f0" : "#E8F5E9" },
            ]}
          >
            <Ionicons
              name="settings-outline"
              size={16}
              color={isRead ? "#999" : "#43A047"}
            />
          </View>
        );
      case "activity":
        return (
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: isRead ? "#f0f0f0" : "#E3F2FD" },
            ]}
          >
            <MaterialIcons
              name="directions-car"
              size={16}
              color={isRead ? "#999" : "#2196F3"}
            />
          </View>
        );
      default:
        return (
          <View
            style={[
              styles.iconContainer,
              { backgroundColor: isRead ? "#f0f0f0" : "#E3F2FD" },
            ]}
          >
            <Ionicons
              name="notifications-outline"
              size={16}
              color={isRead ? "#999" : "#2196F3"}
            />
          </View>
        );
    }
  };

  const renderEmptyState = () => {
    return (
      <View style={styles.emptyContainer}>
        <Image
          source={require("@/assets/images/facebook-icon.png")}
          style={styles.emptyImage}
          resizeMode="contain"
        />
        <Text style={styles.emptyTitle}>Không có thông báo</Text>
        <Text style={styles.emptyText}>
          Bạn sẽ nhận được thông báo về các ưu đãi, cập nhật và hoạt động tại
          đây
        </Text>
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.title}>Danh sách thông báo</Text>
      </View>

      {/* Options Menu (conditionally rendered) */}
      {showOptions && (
        <View style={styles.optionsMenu}>
          <TouchableOpacity style={styles.optionItem}>
            <Ionicons name="checkmark-done-outline" size={18} color="#333" />
            <Text style={styles.optionText}>Đánh dấu tất cả đã đọc</Text>
          </TouchableOpacity>
          <TouchableOpacity style={styles.optionItem}>
            <Ionicons name="trash-outline" size={18} color="#333" />
            <Text style={styles.optionText}>Xóa tất cả thông báo</Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Tabs */}
      <View style={styles.tabContainer}>
        {tabs.map((tab) => (
          <TouchableOpacity
            key={tab.id}
            style={[
              styles.tabButton,
              activeTab === tab.id && styles.activeTabButton,
            ]}
            onPress={() => setActiveTab(tab.id)}
          >
            <Text
              style={[
                styles.tabText,
                activeTab === tab.id && styles.activeTabText,
              ]}
            >
              {tab.label}
            </Text>
            {tab.id === "unread" && (
              <View style={styles.badgeContainer}>
                <Text style={styles.badgeText}>
                  {notifications.filter((item) => !item.isRead).length}
                </Text>
              </View>
            )}
          </TouchableOpacity>
        ))}
      </View>

      {/* Notification list */}
      <FlatList
        data={filteredNotifications}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmptyState}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.notificationCard, !item.isRead && styles.unreadCard]}
          >
            {!item.isRead && <View style={styles.unreadIndicator} />}

            {getNotificationIcon(item.type, item.isRead)}

            <View style={styles.contentWrapper}>
              <View style={styles.notificationHeader}>
                <Text
                  style={[
                    styles.titleText,
                    !item.isRead && styles.unreadTitleText,
                  ]}
                  numberOfLines={1}
                >
                  {item.title}
                </Text>
                <Text style={styles.timeText}>{item.time}</Text>
              </View>

              <Text style={styles.descText} numberOfLines={2}>
                {item.desc}
              </Text>

              <Text style={styles.dateText}>{item.date}</Text>
            </View>
          </TouchableOpacity>
        )}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
    paddingHorizontal: 16,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingTop: 10,
    paddingBottom: 15,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 24,
    fontWeight: "700",
    color: "#222",
  },

  // Options menu
  optionsMenu: {
    position: "absolute",
    right: 16,
    top: 70,
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 5,
    zIndex: 100,
    borderWidth: 1,
    borderColor: "#f0f0f0",
    width: 220,
  },
  optionItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
  },
  optionText: {
    fontSize: 14,
    color: "#333",
    marginLeft: 12,
  },

  // Tabs
  tabContainer: {
    flexDirection: "row",
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#f0f0f0",
  },
  tabButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    marginRight: 8,
    flexDirection: "row",
    alignItems: "center",
  },
  activeTabButton: {
    borderBottomWidth: 2,
    borderBottomColor: Colors.primary,
  },
  tabText: {
    fontSize: 14,
    color: "#666",
    fontWeight: "500",
  },
  activeTabText: {
    color: Colors.primary,
    fontWeight: "600",
  },
  badgeContainer: {
    backgroundColor: "#FF3B30",
    borderRadius: 10,
    minWidth: 20,
    height: 20,
    justifyContent: "center",
    alignItems: "center",
    marginLeft: 6,
    paddingHorizontal: 4,
  },
  badgeText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "bold",
  },

  // List
  listContainer: {
    paddingBottom: 30,
  },

  // Empty state
  emptyContainer: {
    alignItems: "center",
    justifyContent: "center",
    marginTop: 60,
    paddingHorizontal: 30,
  },
  emptyImage: {
    width: 120,
    height: 120,
    marginBottom: 20,
    opacity: 0.8,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginBottom: 10,
  },
  emptyText: {
    color: "#888",
    fontSize: 14,
    textAlign: "center",
    lineHeight: 20,
  },

  // Notification card
  notificationCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    marginBottom: 12,
    padding: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 5,
    elevation: 2,
    borderWidth: 1,
    borderColor: "#f5f5f5",
    position: "relative",
  },
  unreadCard: {
    backgroundColor: "#FAFCFF",
    borderColor: "#E3F2FD",
  },
  unreadIndicator: {
    position: "absolute",
    left: 0,
    top: 16,
    width: 4,
    height: 24,
    backgroundColor: Colors.primary,
    borderTopRightRadius: 4,
    borderBottomRightRadius: 4,
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    position: "absolute",
    left: 16,
    top: 16,
  },
  contentWrapper: {
    marginLeft: 48,
    flex: 1,
  },
  notificationHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 6,
  },
  titleText: {
    fontSize: 15,
    fontWeight: "600",
    color: "#333",
    flex: 1,
    marginRight: 8,
  },
  unreadTitleText: {
    color: "#000",
    fontWeight: "700",
  },
  timeText: {
    fontSize: 12,
    color: "#999",
  },
  descText: {
    fontSize: 13,
    color: "#666",
    lineHeight: 18,
    marginBottom: 8,
  },
  dateText: {
    fontSize: 12,
    color: "#999",
    marginTop: 4,
  },
});
