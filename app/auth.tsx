import { useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { Redirect } from 'expo-router';
import { useAuth } from '@/contexts/AuthContext';
import { Colors } from '@/constants/Colors';

export default function AuthScreen() {
  const { user, isLoading } = useAuth();

  // Nếu đang tải, hiển thị màn hình loading
  if (isLoading) {
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  // Nếu đã đăng nhập, chuyển hướng đến màn hình chính
  if (user) {
    return <Redirect href="/(tabs)" />;
  }

  // Nếu chưa đăng nhập, chuyển hướng đến màn hình đăng nhập
  return <Redirect href="/login-phone" />;
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
});
