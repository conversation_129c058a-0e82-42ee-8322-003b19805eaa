import { Colors } from "@/constants/Colors";
import { useAuth } from "@/contexts/AuthContext";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React, { useEffect, useState } from "react";
import {
  Alert,
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
import * as ImagePicker from "expo-image-picker";

// Import Redux hooks và actions
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updateUserInfo } from "@/store/slices/userSlice";
import { r2Image } from "@/utils/r2-image";
import { updateUserProfile, getPhotoUploadUrl } from "@/api/user.api";

export default function EditProfileScreen() {
  const { user, updateUser } = useAuth();
  const dispatch = useAppDispatch();
  const reduxUser = useAppSelector((state) => state.user);

  // Sử dụng dữ liệu từ Redux nếu có, nếu không thì sử dụng từ AuthContext
  const [name, setName] = useState(
    reduxUser.name || user?.name || "Hiep Nguyen"
  );
  const [photo, setPhoto] = useState<string>(
    reduxUser.photo ||
      (user as any)?.photo ||
      "static/users-photo/RvbodRTR04RzD2Pv.jpg"
  );

  const [isLoading, setIsLoading] = useState(false);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  // Cập nhật state local khi Redux state thay đổi
  useEffect(() => {
    if (reduxUser.name) setName(reduxUser.name);
    if (reduxUser.photo) setPhoto(reduxUser.photo);
  }, [reduxUser]);

  // Chọn ảnh từ máy ảnh
  const takePhoto = async () => {
    try {
      // Yêu cầu quyền truy cập máy ảnh
      const { status } = await ImagePicker.requestCameraPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Lỗi",
          "Bạn cần cấp quyền truy cập máy ảnh để sử dụng tính năng này"
        );
        return;
      }

      setIsUploadingImage(true);

      // Mở máy ảnh để chụp ảnh
      const result = await ImagePicker.launchCameraAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Lấy URI của ảnh đã chụp
        const imageUri = result.assets[0].uri;
        setPhoto(imageUri);
      }
    } catch (error) {
      console.error("Error taking photo:", error);
      Alert.alert("Lỗi", "Không thể chụp ảnh. Vui lòng thử lại sau.");
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Chọn ảnh từ thư viện
  const pickImage = async () => {
    try {
      // Yêu cầu quyền truy cập thư viện ảnh
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();

      if (status !== "granted") {
        Alert.alert(
          "Lỗi",
          "Bạn cần cấp quyền truy cập thư viện ảnh để sử dụng tính năng này"
        );
        return;
      }

      setIsUploadingImage(true);

      // Mở thư viện ảnh để chọn ảnh
      const result = await ImagePicker.launchImageLibraryAsync({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.7,
      });

      if (!result.canceled && result.assets && result.assets.length > 0) {
        // Lấy URI của ảnh đã chọn
        const imageUri = result.assets[0].uri;
        setPhoto(imageUri);
      }
    } catch (error) {
      console.error("Error picking image:", error);
      Alert.alert("Lỗi", "Không thể chọn ảnh. Vui lòng thử lại sau.");
    } finally {
      setIsUploadingImage(false);
    }
  };

  // Hiển thị menu chọn ảnh
  const handleChoosePhoto = () => {
    Alert.alert("Cập nhật ảnh đại diện", "Bạn muốn chọn ảnh từ đâu?", [
      {
        text: "Hủy",
        style: "cancel",
      },
      {
        text: "Máy ảnh",
        onPress: takePhoto,
      },
      {
        text: "Thư viện ảnh",
        onPress: pickImage,
      },
    ]);
  };

  const handleSave = async () => {
    if (!name.trim()) {
      Alert.alert("Lỗi", "Vui lòng nhập tên của bạn");
      return;
    }

    setIsLoading(true);

    try {
      let photoKey: string | null = photo;

      // Kiểm tra nếu ảnh là URI local (từ máy ảnh hoặc thư viện)
      if (photo && photo.startsWith("file://")) {
        try {
          // Lấy presigned URL để upload ảnh
          const presignedUrls = await getPhotoUploadUrl();

          if (presignedUrls && presignedUrls.length > 0) {
            const { url, key } = presignedUrls[0];

            // Tạo form data để upload ảnh
            const formData = new FormData();
            formData.append("file", {
              uri: photo,
              type: "image/jpeg",
              name: "profile.jpg",
            } as any);

            // Upload ảnh lên server
            const uploadResponse = await fetch(url, {
              method: "PUT",
              body: formData,
              headers: {
                "Content-Type": "multipart/form-data",
              },
            });

            if (uploadResponse.ok) {
              // Nếu upload thành công, sử dụng key của ảnh
              photoKey = key;
            } else {
              throw new Error("Không thể tải ảnh lên máy chủ");
            }
          }
        } catch (uploadError) {
          console.error("Error uploading photo:", uploadError);
          Alert.alert(
            "Lỗi",
            "Có lỗi xảy ra khi tải ảnh lên. Thông tin cá nhân vẫn sẽ được cập nhật."
          );
          // Tiếp tục cập nhật thông tin mà không có ảnh mới
          photoKey = null;
        }
      }

      // Gọi API để cập nhật thông tin người dùng
      await updateUserProfile({
        name,
        photo: photoKey || undefined,
      });

      // Cập nhật thông tin trong Redux store
      dispatch(
        updateUserInfo({
          name,
          photo: photoKey || undefined,
        })
      );

      // Cập nhật thông tin trong AuthContext để đồng bộ
      if (updateUser && user) {
        updateUser({
          ...user,
          name,
          photo: photoKey || undefined,
        } as any);
      }

      setIsLoading(false);
      Alert.alert("Thành công", "Thông tin tài khoản đã được cập nhật", [
        {
          text: "OK",
          onPress: () => router.back(),
        },
      ]);
    } catch (err) {
      console.error("Update error:", err);
      setIsLoading(false);
      Alert.alert("Lỗi", "Có lỗi xảy ra khi cập nhật thông tin");
    }
  };

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />

      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color={Colors.primary} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Chỉnh sửa hồ sơ</Text>
        <View style={styles.headerRight} />
      </View>

      <KeyboardAvoidingView
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={{ flex: 1 }}
      >
        <ScrollView
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.scrollContent}
        >
          {/* Avatar Section */}
          <View style={styles.avatarSection}>
            <View style={styles.avatarContainer}>
              {isUploadingImage ? (
                <View style={[styles.avatar, styles.loadingContainer]}>
                  <ActivityIndicator
                    size="large"
                    color={Colors.primary}
                  />
                </View>
              ) : (
                <Image source={{ uri: r2Image(photo) }} style={styles.avatar} />
              )}
              <TouchableOpacity
                style={styles.editAvatarButton}
                onPress={handleChoosePhoto}
                disabled={isUploadingImage}
              >
                <Ionicons name="camera" size={18} color="#fff" />
              </TouchableOpacity>
            </View>
          </View>

          {/* Form Section */}
          <View style={styles.formSection}>
            <View style={styles.inputGroup}>
              <Text style={styles.label}>Họ và tên</Text>
              <TextInput
                style={styles.input}
                value={name}
                onChangeText={setName}
                placeholder="Nhập họ và tên"
              />
            </View>

            <View style={styles.inputGroup}>
              <Text style={styles.label}>Số điện thoại</Text>
              <TextInput
                style={[styles.input, styles.disabledInput]}
                value={reduxUser.phone || user?.phone || ""}
                placeholder="Nhập số điện thoại"
                keyboardType="phone-pad"
                editable={false} // Không cho phép sửa số điện thoại
              />
              <Text style={styles.helperText}>
                Số điện thoại không thể thay đổi
              </Text>
            </View>

            <TouchableOpacity
              style={[styles.saveButton, isLoading && styles.disabledButton]}
              onPress={handleSave}
              disabled={isLoading}
            >
              <Text style={styles.saveButtonText}>
                {isLoading ? "Đang lưu..." : "Lưu thay đổi"}
              </Text>
            </TouchableOpacity>
          </View>
        </ScrollView>
      </KeyboardAvoidingView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingTop: Platform.OS === "ios" ? 48 : 16,
    paddingHorizontal: 16,
    paddingBottom: 8,
    backgroundColor: "#fff",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  headerRight: {
    width: 40,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 20,
  },
  scrollContent: {
    paddingBottom: 40,
  },
  avatarSection: {
    alignItems: "center",
    paddingVertical: 12,
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    marginBottom: 16,
  },
  avatarContainer: {
    position: "relative",
    marginBottom: 12,
  },
  avatar: {
    width: 110,
    height: 110,
    borderRadius: 55,
    borderWidth: 3,
    borderColor: "#fff",
  },
  loadingContainer: {
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f0f0f0",
  },
  editAvatarButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "#fff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 3,
  },
  formSection: {
    padding: 20,
  },
  inputGroup: {
    marginBottom: 20,
  },
  label: {
    fontSize: 14,
    fontWeight: "600",
    color: "#555",
    marginBottom: 8,
  },
  disabledInput: {
    backgroundColor: "#f2f2f2",
  },
  input: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    fontSize: 16,
    color: "#333",
  },
  helperText: {
    fontSize: 12,
    color: "#888",
    marginTop: 4,
    fontStyle: "italic",
  },
  saveButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 14,
    borderRadius: 8,
    alignItems: "center",
    marginTop: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  disabledButton: {
    backgroundColor: "#cccccc",
  },
  saveButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
});
