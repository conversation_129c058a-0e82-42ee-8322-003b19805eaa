import React, { useState } from "react";
import {
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
} from "react-native";
import { Stack, Link, router } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { ThemedText } from "@/components/ThemedText";
import { Input } from "@/components/ui/Input";
import { Button } from "@/components/ui/Button";
import { Colors } from "@/constants/Colors";
import { useAuth } from "@/contexts/AuthContext";

export default function ForgotPasswordScreen() {
  const { resetPassword } = useAuth();
  const [phone, setPhone] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const validateForm = () => {
    if (!phone) {
      setError("Số điện thoại không được để trống");
      return false;
    } else if (!/^[0-9]{10,11}$/.test(phone.replace(/[^0-9]/g, ""))) {
      setError("Số điện thoại không hợp lệ");
      return false;
    }

    setError("");
    return true;
  };

  const handleResetPassword = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      // Gọi hàm resetPassword từ AuthContext
      const result = await resetPassword(phone);

      if (result) {
        // Chuyển hướng đến màn hình nhập OTP
        router.push({
          pathname: "/verify-otp",
          params: { phone },
        });
      } else {
        setError("Không thể gửi mã xác nhận. Vui lòng thử lại sau.");
      }
    } catch (error) {
      console.error("Reset password error:", error);
      setError("Đã xảy ra lỗi. Vui lòng thử lại sau.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <StatusBar style="dark" />
      <Stack.Screen
        options={{
          headerTitle: "Quên mật khẩu",
          headerTitleAlign: "center",
          headerBackTitle: "Trở lại",
        }}
      />

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require("@/assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.formContainer}>
          <View style={styles.form}>
            <Input
              label="Số điện thoại"
              placeholder="Nhập số điện thoại của bạn"
              keyboardType="phone-pad"
              value={phone}
              onChangeText={(text) => {
                setPhone(text);
                setError("");
              }}
              error={error}
              leftIcon="phone"
            />

            <Button
              title="Gửi mã xác nhận"
              onPress={handleResetPassword}
              loading={isLoading}
              style={styles.submitButton}
            />
          </View>

          <View style={styles.footer}>
            <ThemedText style={styles.footerText}>
              Bạn muốn đăng nhập?{" "}
            </ThemedText>
            <Link href="/login-phone" asChild>
              <TouchableOpacity>
                <ThemedText style={styles.loginLink}>Đăng nhập</ThemedText>
              </TouchableOpacity>
            </Link>
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  scrollContent: {
    flexGrow: 1,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 40,
    marginBottom: 20,
  },
  logo: {
    width: 200,
    height: 120,
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  subtitle: {
    textAlign: "center",
    color: "#687076",
    marginBottom: 32,
    lineHeight: 22,
  },
  form: {
    marginBottom: 24,
  },
  submitButton: {
    marginTop: 16,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: 16,
    marginBottom: 32,
  },
  footerText: {
    color: "#687076",
  },
  loginLink: {
    color: Colors.primary,
    fontWeight: "600",
  },
});
