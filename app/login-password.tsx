import { ThemedText } from "@/components/ThemedText";
import { Button } from "@/components/ui/Button";
import { PasswordInput } from "@/components/ui/PasswordInput";
import { Colors } from "@/constants/Colors";
import { useAuth } from "@/contexts/AuthContext";
import { Ionicons } from "@expo/vector-icons";
import { Link, router, useLocalSearchParams } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React, { useState } from "react";
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from "react-native";

export default function LoginPasswordScreen() {
  const { signIn } = useAuth();
  const params = useLocalSearchParams<{ phone: string }>();
  const phone = params.phone || "";

  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  const validateForm = () => {
    if (!password) {
      setError("Mật khẩu không được để trống");
      return false;
    } else if (password.length !== 6) {
      setError("Mật khẩu phải có đúng 6 ký tự");
      return false;
    }

    setError("");
    return true;
  };

  const handleLogin = async () => {
    if (!validateForm()) return;

    setIsLoading(true);

    try {
      const success = await signIn(phone, password);
      if (success) {
        router.replace("/(tabs)");
      } else {
        setError("Tài khoản không đúng. Vui lòng thử lại.");
      }
    } catch (error) {
      console.error("Login error:", error);
      setError("Đã xảy ra lỗi. Vui lòng thử lại sau.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    router.back();
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <StatusBar style="dark" />

      <TouchableOpacity
        style={styles.backButtonAbsolute}
        onPress={handleBack}
      >
        <Ionicons name="arrow-back" size={24} color={Colors.primary} />
      </TouchableOpacity>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require("@/assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.formContainer}>
          <View style={styles.phoneDisplay}>
            <ThemedText style={styles.phoneLabel}>Số điện thoại</ThemedText>
            <ThemedText style={styles.phoneValue}>{phone}</ThemedText>
          </View>

          <View style={styles.form}>
            <PasswordInput
              label="Mật khẩu"
              value={password}
              onChangeText={(text) => {
                setPassword(text);
                setError("");
              }}
              error={error}
            />

            <Link href="/forgot-password" asChild>
              <TouchableOpacity style={styles.forgotPassword}>
                <ThemedText style={styles.forgotPasswordText}>
                  Quên mật khẩu?
                </ThemedText>
              </TouchableOpacity>
            </Link>

            <Button
              title="Đăng nhập"
              onPress={handleLogin}
              loading={isLoading}
              style={styles.loginButton}
            />
          </View>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backButtonAbsolute: {
    position: 'absolute',
    top: 50,
    left: 20,
    zIndex: 10,
    width: 40,
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  scrollContent: {
    flexGrow: 1,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 52,
    marginBottom: 20,
  },
  logo: {
    width: 200,
    height: 120,
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  subtitle: {
    textAlign: "center",
    color: "#687076",
    marginBottom: 24,
  },
  phoneDisplay: {
    backgroundColor: "#F5F7F8",
    borderRadius: 8,
    padding: 16,
    marginBottom: 24,
  },
  phoneLabel: {
    fontSize: 14,
    color: "#687076",
    marginBottom: 4,
  },
  phoneValue: {
    fontSize: 16,
    fontWeight: "bold",
  },
  form: {
    marginBottom: 24,
  },
  forgotPassword: {
    alignSelf: "flex-end",
    marginBottom: 24,
  },
  forgotPasswordText: {
    color: Colors.primary,
    fontWeight: "600",
  },
  loginButton: {
    marginTop: 8,
  },
  backButton: {
    paddingHorizontal: 8,
  },
  backButtonText: {
    color: Colors.primary,
    fontWeight: "600",
  },
});
