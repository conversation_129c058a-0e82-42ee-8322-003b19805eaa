import { Ionicons } from "@expo/vector-icons";
import { getPaymentMethods, PaymentMethod } from "@/api/payment-methods.api";
import { router, useLocalSearchParams } from "expo-router";
import React, { useState, useEffect, useCallback, useRef } from "react";
import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  SafeAreaView,
  StatusBar,
  Image,
  Animated,
  Easing,
} from "react-native";
import { Colors } from "@/constants/Colors";

export default function PaymentMethodSelection() {
  const params = useLocalSearchParams();
  const currentPaymentMethod = params.currentPaymentMethod as string || "CASH";
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState(currentPaymentMethod);
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Animation value for skeleton loading effect
  const shimmerAnimation = useRef(new Animated.Value(0)).current;

  // Start the shimmer animation when component mounts
  useEffect(() => {
    const startShimmerAnimation = () => {
      Animated.loop(
        Animated.timing(shimmerAnimation, {
          toValue: 1,
          duration: 1500,
          easing: Easing.linear,
          useNativeDriver: false,
        })
      ).start();
    };

    if (isLoading) {
      startShimmerAnimation();
    }

    return () => {
      shimmerAnimation.stopAnimation();
    };
  }, [isLoading, shimmerAnimation]);

  // Memoize the fetch function to prevent unnecessary API calls
  const fetchPaymentMethods = useCallback(async () => {
    // Skip fetching if we already have payment methods
    if (paymentMethods.length > 0) {
      return;
    }

    setIsLoading(true);
    try {
      const methods = await getPaymentMethods();
      setPaymentMethods(methods);
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    } finally {
      setIsLoading(false);
    }
  }, [paymentMethods.length]);

  // Fetch payment methods when component mounts
  useEffect(() => {
    fetchPaymentMethods();
  }, [fetchPaymentMethods]);

  const handleSelectPaymentMethod = (methodCode: string) => {
    setSelectedPaymentMethod(methodCode);
    // Return to the previous screen with the selected payment method
    router.back();
    // Pass the selected payment method back to the previous screen
    router.setParams({ selectedPaymentMethod: methodCode });
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="default" />

      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Phương thức thanh toán</Text>
        <View style={styles.placeholder} />
      </View>

      {/* Payment Methods List */}
      {isLoading ? (
        <View style={styles.skeletonContainer}>
          {/* Render 5 skeleton items */}
          {[...Array(5)].map((_, index) => (
            <React.Fragment key={index}>
              <View style={styles.paymentMethodItem}>
                <View style={styles.skeletonIconContainer}>
                  <Animated.View
                    style={[
                      styles.skeletonIcon,
                      {
                        backgroundColor: shimmerAnimation.interpolate({
                          inputRange: [0, 1],
                          outputRange: ['#f0f0f0', '#e0e0e0']
                        })
                      }
                    ]}
                  />
                </View>
                <View style={styles.skeletonTextContainer}>
                  <Animated.View
                    style={[
                      styles.skeletonText,
                      {
                        backgroundColor: shimmerAnimation.interpolate({
                          inputRange: [0, 1],
                          outputRange: ['#f0f0f0', '#e0e0e0']
                        }),
                        width: `${70 + (index % 3) * 10}%` // Vary the width slightly
                      }
                    ]}
                  />
                </View>
              </View>
              {index < 4 && <View style={styles.separator} />}
            </React.Fragment>
          ))}
        </View>
      ) : (
        <FlatList
          data={paymentMethods}
          keyExtractor={(item) => item.id.toString()}
          contentContainerStyle={styles.listContainer}
          renderItem={({ item }) => (
            <TouchableOpacity
              style={[
                styles.paymentMethodItem,
                selectedPaymentMethod === item.code && styles.selectedPaymentMethod,
              ]}
              onPress={() => handleSelectPaymentMethod(item.code)}
            >
              <View style={styles.iconContainer}>
                <Image
                  source={{ uri: item.icon }}
                  style={styles.paymentMethodImage}
                  resizeMode="contain"
                />
              </View>

              <Text style={[
                styles.paymentMethodName,
                selectedPaymentMethod === item.code && styles.selectedPaymentMethodText
              ]}>
                {item.name}
              </Text>

              {selectedPaymentMethod === item.code && (
                <Ionicons name="checkmark-circle" size={24} color={Colors.primary} style={styles.checkIcon} />
              )}
            </TouchableOpacity>
          )}
          ItemSeparatorComponent={() => <View style={styles.separator} />}
        />
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: "white",
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  backButton: {
    padding: 8,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  placeholder: {
    width: 40,
  },
  // Skeleton styles
  skeletonContainer: {
    padding: 16,
  },
  skeletonIconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f5f5f5",
    marginRight: 16,
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  skeletonIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#e0e0e0",
  },
  skeletonTextContainer: {
    flex: 1,
    height: 20,
    justifyContent: "center",
  },
  skeletonText: {
    height: 16,
    backgroundColor: "#e0e0e0",
    borderRadius: 4,
    width: "70%",
  },
  listContainer: {
    padding: 16,
  },
  paymentMethodItem: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "white",
    padding: 16,
    borderRadius: 12,
  },
  selectedPaymentMethod: {
    backgroundColor: "#fff8e1",
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#f5f5f5",
    alignItems: "center",
    justifyContent: "center",
    marginRight: 16,
  },
  paymentMethodImage: {
    width: 24,
    height: 24,
  },
  paymentMethodName: {
    fontSize: 16,
    color: "#333",
    flex: 1,
  },
  selectedPaymentMethodText: {
    fontWeight: "600",
    color: "#000",
  },
  checkIcon: {
    marginLeft: 8,
  },
  separator: {
    height: 12,
  },
});
