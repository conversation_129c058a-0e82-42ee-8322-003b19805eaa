import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  SafeAreaView,
  ScrollView,
  Image,
  TouchableOpacity,
  TextInput,
  Alert,
} from 'react-native';
import { Stack, useLocalSearchParams, router } from 'expo-router';
import { Ionicons, MaterialIcons, FontAwesome } from '@expo/vector-icons';
import { Colors } from '@/constants/Colors';

export default function RideDetailScreen() {
  const params = useLocalSearchParams();
  const { id, service, from, to, date, time, price, status } = params;
  
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Thông tin tài xế (giả lập)
  const driver = {
    name: 'Nguyễn Văn A',
    avatar: 'https://i.imgur.com/0y8Ftya.png',
    rating: 4.8,
    trips: 1243,
    vehicleNumber: '51F-123.45',
    vehicleModel: 'Honda SH 150i',
  };

  const handleRating = (value: number) => {
    setRating(value);
  };

  const handleSubmitRating = () => {
    if (rating === 0) {
      Alert.alert('Thông báo', 'Vui lòng chọn số sao đánh giá');
      return;
    }

    setIsSubmitting(true);
    
    // Giả lập gửi đánh giá
    setTimeout(() => {
      setIsSubmitting(false);
      Alert.alert(
        'Thành công',
        'Cảm ơn bạn đã đánh giá chuyến đi!',
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    }, 1000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Chi tiết chuyến đi',
          headerTitleAlign: 'center',
        }}
      />

      <ScrollView showsVerticalScrollIndicator={false}>
        {/* Thông tin chuyến đi */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <View style={styles.serviceInfo}>
              <View style={styles.iconContainer}>
                <MaterialIcons name="directions-car" size={20} color="#fff" />
              </View>
              <View>
                <Text style={styles.serviceText}>{service}</Text>
                <Text style={styles.dateText}>{date} • {time}</Text>
              </View>
            </View>
            <View style={styles.statusContainer}>
              <Text style={styles.statusText}>{status}</Text>
            </View>
          </View>

          <View style={styles.divider} />

          {/* Lộ trình */}
          <View style={styles.routeContainer}>
            <View style={styles.routeIcons}>
              <View style={styles.originDot} />
              <View style={styles.routeLine} />
              <View style={styles.destinationDot} />
            </View>
            
            <View style={styles.routeDetails}>
              <Text style={styles.routeText}>{from}</Text>
              <Text style={styles.routeText}>{to}</Text>
            </View>
          </View>

          <View style={styles.divider} />

          {/* Thông tin thanh toán */}
          <View style={styles.paymentInfo}>
            <Text style={styles.sectionTitle}>Thông tin thanh toán</Text>
            
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Phương thức thanh toán</Text>
              <Text style={styles.paymentValue}>Tiền mặt</Text>
            </View>
            
            <View style={styles.paymentRow}>
              <Text style={styles.paymentLabel}>Tổng cộng</Text>
              <Text style={styles.paymentTotal}>{price}</Text>
            </View>
          </View>
        </View>

        {/* Thông tin tài xế */}
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>Thông tin tài xế</Text>
          
          <View style={styles.driverInfo}>
            <Image source={{ uri: driver.avatar }} style={styles.driverAvatar} />
            <View style={styles.driverDetails}>
              <Text style={styles.driverName}>{driver.name}</Text>
              <View style={styles.driverRating}>
                <Ionicons name="star" size={14} color="#FFD700" />
                <Text style={styles.ratingText}>{driver.rating} • {driver.trips} chuyến</Text>
              </View>
              <Text style={styles.vehicleInfo}>
                {driver.vehicleModel} • {driver.vehicleNumber}
              </Text>
            </View>
          </View>
        </View>

        {/* Đánh giá tài xế */}
        <View style={styles.card}>
          <Text style={styles.sectionTitle}>Đánh giá tài xế</Text>
          
          <View style={styles.ratingContainer}>
            <Text style={styles.ratingLabel}>Bạn đánh giá chuyến đi này như thế nào?</Text>
            
            <View style={styles.starsContainer}>
              {[1, 2, 3, 4, 5].map((star) => (
                <TouchableOpacity 
                  key={star} 
                  onPress={() => handleRating(star)}
                  style={styles.starButton}
                >
                  <FontAwesome 
                    name={rating >= star ? "star" : "star-o"} 
                    size={32} 
                    color={rating >= star ? "#FFD700" : "#CCCCCC"} 
                  />
                </TouchableOpacity>
              ))}
            </View>
            
            <TextInput
              style={styles.commentInput}
              placeholder="Nhận xét về chuyến đi (không bắt buộc)"
              multiline
              numberOfLines={3}
              value={comment}
              onChangeText={setComment}
            />
            
            <TouchableOpacity 
              style={[
                styles.submitButton, 
                (rating === 0 || isSubmitting) && styles.disabledButton
              ]}
              onPress={handleSubmitRating}
              disabled={rating === 0 || isSubmitting}
            >
              <Text style={styles.submitButtonText}>
                {isSubmitting ? 'Đang gửi...' : 'Gửi đánh giá'}
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Hỗ trợ */}
        <TouchableOpacity style={styles.supportButton}>
          <Ionicons name="help-circle-outline" size={20} color={Colors.primary} />
          <Text style={styles.supportText}>Liên hệ hỗ trợ</Text>
        </TouchableOpacity>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8f8f8',
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 12,
    marginHorizontal: 16,
    marginTop: 16,
    padding: 16,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  serviceInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: '#4c66ef',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  serviceText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#222',
  },
  dateText: {
    fontSize: 13,
    color: '#888',
    marginTop: 2,
  },
  statusContainer: {
    paddingHorizontal: 10,
    paddingVertical: 4,
    backgroundColor: '#E8F5E9',
    borderRadius: 12,
  },
  statusText: {
    color: '#43A047',
    fontSize: 12,
    fontWeight: '600',
  },
  divider: {
    height: 1,
    backgroundColor: '#f0f0f0',
    marginVertical: 16,
  },
  routeContainer: {
    flexDirection: 'row',
    marginBottom: 8,
  },
  routeIcons: {
    width: 16,
    alignItems: 'center',
    marginRight: 12,
  },
  originDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.primary,
  },
  routeLine: {
    width: 2,
    height: 30,
    backgroundColor: '#ddd',
    marginVertical: 4,
  },
  destinationDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#ff5722',
  },
  routeDetails: {
    flex: 1,
    justifyContent: 'space-between',
    height: 60,
  },
  routeText: {
    fontSize: 14,
    color: '#555',
    paddingVertical: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: '#222',
    marginBottom: 16,
  },
  paymentInfo: {
    marginBottom: 8,
  },
  paymentRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  paymentLabel: {
    fontSize: 14,
    color: '#666',
  },
  paymentValue: {
    fontSize: 14,
    color: '#222',
    fontWeight: '500',
  },
  paymentTotal: {
    fontSize: 16,
    color: '#222',
    fontWeight: '700',
  },
  driverInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  driverAvatar: {
    width: 60,
    height: 60,
    borderRadius: 30,
    marginRight: 16,
  },
  driverDetails: {
    flex: 1,
  },
  driverName: {
    fontSize: 16,
    fontWeight: '600',
    color: '#222',
    marginBottom: 4,
  },
  driverRating: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  ratingText: {
    fontSize: 13,
    color: '#666',
    marginLeft: 4,
  },
  vehicleInfo: {
    fontSize: 13,
    color: '#666',
  },
  ratingContainer: {
    alignItems: 'center',
  },
  ratingLabel: {
    fontSize: 14,
    color: '#666',
    marginBottom: 16,
    textAlign: 'center',
  },
  starsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 20,
  },
  starButton: {
    marginHorizontal: 8,
  },
  commentInput: {
    width: '100%',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    padding: 12,
    fontSize: 14,
    color: '#333',
    textAlignVertical: 'top',
    marginBottom: 20,
  },
  submitButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 8,
    alignItems: 'center',
    width: '100%',
  },
  disabledButton: {
    backgroundColor: '#cccccc',
  },
  submitButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
  supportButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: 24,
    marginHorizontal: 16,
    paddingVertical: 12,
  },
  supportText: {
    color: Colors.primary,
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 8,
  },
});
