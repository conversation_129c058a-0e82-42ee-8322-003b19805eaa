import React from "react";
import {
  FlatList,
  Image,
  SafeAreaView,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
  Dimensions,
} from "react-native";
import { StatusBar } from "expo-status-bar";
import { Ionicons, MaterialIcons, Feather } from "@expo/vector-icons";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Mock data for food categories
interface Category {
  id: string;
  name: string;
  image: any;
}

// Mock data for promotions
interface Promotion {
  id: string;
  title: string;
  discount: string;
  description: string;
  image: any;
  backgroundColor: string;
  textColor: string;
}

// Mock data for restaurants
interface Restaurant {
  id: string;
  name: string;
  image: any;
  rating: number;
  ratingCount: number;
  distance: string;
  deliveryFee: string;
  estimatedTime: string;
  isFavorite: boolean;
}

// Categories data
const CATEGORIES: Category[] = [
  {
    id: "1",
    name: "Chicken",
    image: require("@/assets/images/restaurants/res1.png"),
  },
  {
    id: "2",
    name: "<PERSON>",
    image: require("@/assets/images/restaurants/res2.png"),
  },
  {
    id: "3",
    name: "Pizza",
    image: require("@/assets/images/the-cafes/cf1.png"),
  },
  {
    id: "4",
    name: "Noodles",
    image: require("@/assets/images/the-cafes/cf2.png"),
  },
  {
    id: "5",
    name: "Dessert",
    image: require("@/assets/images/the-cafes/cf3.png"),
  },
];

// Promotions data
const PROMOTIONS: Promotion[] = [
  {
    id: "1",
    title: "Get",
    discount: "25%OFF",
    description: "On Beef Pizza",
    image: require("@/assets/images/the-cafes/cf1.png"),
    backgroundColor: "#5D4037",
    textColor: "#FFFFFF",
  },
  {
    id: "2",
    title: "Big Pizza",
    discount: "Holiday",
    description: "Combo",
    image: require("@/assets/images/the-cafes/cf1.png"),
    backgroundColor: "#D84315",
    textColor: "#FFFFFF",
  },
];

// Restaurants data
const RESTAURANTS: Restaurant[] = [
  {
    id: "1",
    name: "Pizza Stories",
    image: require("@/assets/images/the-cafes/cf1.png"),
    rating: 4.9,
    ratingCount: 120,
    distance: "2.0 km",
    deliveryFee: "$3.00 delivery fee",
    estimatedTime: "25-30 min",
    isFavorite: true,
  },
  {
    id: "2",
    name: "Burger King",
    image: require("@/assets/images/restaurants/res2.png"),
    rating: 4.7,
    ratingCount: 150,
    distance: "1.5 km",
    deliveryFee: "$2.50 delivery fee",
    estimatedTime: "15-20 min",
    isFavorite: false,
  },
  {
    id: "3",
    name: "Chicken Republic",
    image: require("@/assets/images/restaurants/res1.png"),
    rating: 4.5,
    ratingCount: 200,
    distance: "3.0 km",
    deliveryFee: "$4.00 delivery fee",
    estimatedTime: "30-40 min",
    isFavorite: true,
  },
];

export default function BookingScreen() {
  const insets = useSafeAreaInsets();
  const { width } = Dimensions.get("window");

  // Render category item
  const renderCategoryItem = ({ item }: { item: Category }) => (
    <TouchableOpacity style={styles.categoryItem}>
      <Image source={item.image} style={styles.categoryImage} />
      <Text style={styles.categoryName}>{item.name}</Text>
    </TouchableOpacity>
  );

  // Render promotion item
  const renderPromotionItem = ({ item }: { item: Promotion }) => (
    <TouchableOpacity
      style={[
        styles.promotionItem,
        { backgroundColor: item.backgroundColor, width: width * 0.65 },
      ]}
    >
      <View style={styles.promotionContent}>
        <Text style={[styles.promotionTitle, { color: item.textColor }]}>
          {item.title}
        </Text>
        <Text style={[styles.promotionDiscount, { color: item.textColor }]}>
          {item.discount}
        </Text>
        <Text style={[styles.promotionDescription, { color: item.textColor }]}>
          {item.description}
        </Text>
      </View>
      <Image source={item.image} style={styles.promotionImage} />
    </TouchableOpacity>
  );

  // Render restaurant item
  const renderRestaurantItem = ({ item }: { item: Restaurant }) => (
    <TouchableOpacity style={styles.restaurantItem}>
      <View style={styles.restaurantImageContainer}>
        <Image source={item.image} style={styles.restaurantImage} />
        <TouchableOpacity style={styles.favoriteButton}>
          <Ionicons
            name={item.isFavorite ? "heart" : "heart-outline"}
            size={24}
            color={item.isFavorite ? "#FF5252" : "#FFFFFF"}
          />
        </TouchableOpacity>
        <View style={styles.timeTag}>
          <Text style={styles.timeText}>{item.estimatedTime}</Text>
        </View>
      </View>
      <View style={styles.restaurantInfo}>
        <Text style={styles.restaurantName}>{item.name}</Text>
        <View style={styles.ratingContainer}>
          <Ionicons name="star" size={16} color="#FFC107" />
          <Text style={styles.ratingText}>
            {item.rating} ({item.ratingCount})
          </Text>
        </View>
        <View style={styles.restaurantDetails}>
          <View style={styles.detailItem}>
            <Ionicons name="location-outline" size={14} color="#757575" />
            <Text style={styles.detailText}>{item.distance}</Text>
          </View>
          <View style={styles.detailItem}>
            <MaterialIcons name="delivery-dining" size={14} color="#757575" />
            <Text style={styles.detailText}>{item.deliveryFee}</Text>
          </View>
        </View>
      </View>
    </TouchableOpacity>
  );

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <ScrollView
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header */}
        <View style={styles.header}>
          <View style={styles.locationContainer}>
            <Text style={styles.locationLabel}>Your Location</Text>
            <View style={styles.locationRow}>
              <Text style={styles.locationText}>
                3899 Glen Thomas Drive, NY
              </Text>
              <Ionicons name="chevron-down" size={16} color="#757575" />
            </View>
          </View>
          <TouchableOpacity style={styles.notificationButton}>
            <Ionicons name="notifications-outline" size={24} color="#212121" />
          </TouchableOpacity>
        </View>

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <View style={styles.searchBar}>
            <Ionicons
              name="search"
              size={20}
              color="#757575"
              style={styles.searchIcon}
            />
            <TextInput
              style={styles.searchInput}
              placeholder="Search foods, groceries"
              placeholderTextColor="#9E9E9E"
            />
          </View>
          <TouchableOpacity style={styles.filterButton}>
            <Feather name="sliders" size={20} color="#FFFFFF" />
          </TouchableOpacity>
        </View>

        {/* Categories */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Categories</Text>
          <TouchableOpacity>
            <Text style={styles.seeAllText}>See all</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={CATEGORIES}
          renderItem={renderCategoryItem}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.categoriesList}
        />

        {/* Promotions */}
        <FlatList
          data={PROMOTIONS}
          renderItem={renderPromotionItem}
          keyExtractor={(item) => item.id}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.promotionsList}
        />

        {/* Top Restaurants */}
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Top Restaurants</Text>
          <TouchableOpacity>
            <Text style={styles.seeAllText}>See all</Text>
          </TouchableOpacity>
        </View>
        <FlatList
          data={RESTAURANTS}
          renderItem={renderRestaurantItem}
          keyExtractor={(item) => item.id}
          scrollEnabled={false}
          contentContainerStyle={styles.restaurantsList}
        />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#FFFFFF",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    paddingTop: 8,
    paddingBottom: 16,
  },
  locationContainer: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    color: "#9E9E9E",
  },
  locationRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  locationText: {
    fontSize: 16,
    fontWeight: "600",
    color: "#212121",
    marginRight: 4,
  },
  notificationButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
  },
  searchContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 16,
  },
  searchBar: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#F5F5F5",
    borderRadius: 25,
    paddingHorizontal: 16,
    paddingVertical: 10,
    marginRight: 12,
  },
  searchIcon: {
    marginRight: 8,
  },
  searchInput: {
    flex: 1,
    fontSize: 14,
    color: "#212121",
  },
  filterButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: "#FF5722",
    justifyContent: "center",
    alignItems: "center",
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingHorizontal: 16,
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "700",
    color: "#212121",
  },
  seeAllText: {
    fontSize: 14,
    color: "#FF5722",
  },
  categoriesList: {
    paddingLeft: 16,
    paddingRight: 8,
  },
  categoryItem: {
    alignItems: "center",
    marginRight: 16,
    width: 70,
  },
  categoryImage: {
    width: 70,
    height: 70,
    borderRadius: 35,
    marginBottom: 8,
  },
  categoryName: {
    fontSize: 12,
    fontWeight: "500",
    color: "#212121",
    textAlign: "center",
  },
  promotionsList: {
    paddingLeft: 16,
    paddingRight: 8,
    marginTop: 16,
    marginBottom: 24,
  },
  promotionItem: {
    flexDirection: "row",
    borderRadius: 12,
    overflow: "hidden",
    marginRight: 16,
    height: 120,
  },
  promotionContent: {
    flex: 1,
    padding: 16,
    justifyContent: "center",
  },
  promotionTitle: {
    fontSize: 14,
    fontWeight: "500",
  },
  promotionDiscount: {
    fontSize: 22,
    fontWeight: "bold",
    marginVertical: 4,
  },
  promotionDescription: {
    fontSize: 14,
  },
  promotionImage: {
    width: 100,
    height: 100,
    resizeMode: "cover",
    alignSelf: "flex-end",
    marginRight: 10,
    marginTop: 10,
  },
  restaurantsList: {
    paddingHorizontal: 16,
    paddingBottom: 80,
  },
  restaurantItem: {
    marginBottom: 24,
  },
  restaurantImageContainer: {
    position: "relative",
    borderRadius: 12,
    overflow: "hidden",
    height: 180,
  },
  restaurantImage: {
    width: "100%",
    height: "100%",
    resizeMode: "cover",
  },
  favoriteButton: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(0, 0, 0, 0.3)",
    justifyContent: "center",
    alignItems: "center",
  },
  timeTag: {
    position: "absolute",
    bottom: 12,
    left: 12,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    paddingHorizontal: 10,
    paddingVertical: 4,
    borderRadius: 4,
  },
  timeText: {
    color: "#FFFFFF",
    fontSize: 12,
    fontWeight: "500",
  },
  restaurantInfo: {
    marginTop: 12,
  },
  restaurantName: {
    fontSize: 18,
    fontWeight: "700",
    color: "#212121",
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 6,
  },
  ratingText: {
    fontSize: 14,
    color: "#212121",
    marginLeft: 4,
  },
  restaurantDetails: {
    flexDirection: "row",
    alignItems: "center",
  },
  detailItem: {
    flexDirection: "row",
    alignItems: "center",
    marginRight: 16,
  },
  detailText: {
    fontSize: 12,
    color: "#757575",
    marginLeft: 4,
  },
  bottomNav: {
    flexDirection: "row",
    justifyContent: "space-around",
    alignItems: "center",
    backgroundColor: "#FFFFFF",
    borderTopWidth: 1,
    borderTopColor: "#F5F5F5",
    paddingVertical: 10,
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
  },
  navItem: {
    alignItems: "center",
    justifyContent: "center",
    height: 50,
    width: 60,
  },
  activeNavItem: {
    position: "relative",
  },
  activeIndicator: {
    position: "absolute",
    bottom: 0,
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: "#FF5722",
  },
});
