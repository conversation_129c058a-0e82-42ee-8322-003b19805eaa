import { Colors } from "@/constants/Colors";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { router } from "expo-router";
import { StatusBar } from "expo-status-bar";
import React, { useState } from "react";
import {
  ActivityIndicator,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Import Redux hooks và actions
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { clearSearchResult, searchFines } from "@/store/slices/fineSlice";

export default function FineServiceScreen() {
  const [licensePlate, setLicensePlate] = useState("");
  const insets = useSafeAreaInsets();

  // Sử dụng Redux hooks
  const dispatch = useAppDispatch();
  const { searchResult, isLoading, error, searchHistory } = useAppSelector(
    (state) => state.fine
  );

  // Xử lý quay lại màn hình trước đó
  const handleBack = () => {
    router.back();
  };

  // Xử lý tìm kiếm phạt nguội
  const handleSearch = () => {
    if (!licensePlate.trim()) {
      alert("Vui lòng nhập biển số xe");
      return;
    }

    // Dispatch action để tìm kiếm phạt nguội
    dispatch(searchFines(licensePlate));
  };

  // Xử lý xóa kết quả tìm kiếm
  const handleClearSearch = () => {
    setLicensePlate("");
    dispatch(clearSearchResult());
  };

  // Hiển thị lịch sử tìm kiếm gần đây
  const handleSelectFromHistory = (plate: string) => {
    setLicensePlate(plate);
    dispatch(searchFines(plate));
  };

  return (
    <View style={styles.container}>
      <StatusBar style="dark" />

      {/* Nút back custom */}
      <View style={[styles.backButtonContainer, { top: insets.top + 10 }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color="black" />
        </TouchableOpacity>
      </View>

      <ScrollView
        style={styles.content}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Image
            source={require("@/assets/images/features/inspection.png")}
            style={styles.headerImage}
            resizeMode="contain"
          />
          <Text style={styles.title}>Tra cứu phạt nguội</Text>
        </View>

        <View style={styles.searchSection}>
          <Text style={styles.sectionTitle}>Nhập biển số xe</Text>

          <View style={styles.searchInputContainer}>
            <TextInput
              style={styles.searchInput}
              placeholder="VD: 51F-123.45"
              value={licensePlate}
              onChangeText={setLicensePlate}
              autoCapitalize="characters"
              editable={!isLoading}
            />

            <TouchableOpacity
              style={[styles.searchButton, isLoading && styles.disabledButton]}
              onPress={handleSearch}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <ActivityIndicator
                    size="small"
                    color="#fff"
                    style={{ marginRight: 8 }}
                  />
                  <Text style={styles.searchButtonText}>Đang tìm...</Text>
                </>
              ) : (
                <Text style={styles.searchButtonText}>Tra cứu</Text>
              )}
            </TouchableOpacity>
          </View>

          {searchResult && (
            <TouchableOpacity
              style={styles.clearButton}
              onPress={handleClearSearch}
            >
              <Text style={styles.clearButtonText}>Xóa kết quả</Text>
            </TouchableOpacity>
          )}

          {/* Hiển thị lỗi nếu có */}
          {error && <Text style={styles.errorText}>{error}</Text>}

          {/* Hiển thị lịch sử tìm kiếm */}
          {searchHistory.length > 0 && !searchResult && (
            <View style={styles.historySection}>
              <Text style={styles.historyTitle}>Lịch sử tra cứu gần đây</Text>
              <ScrollView
                horizontal
                showsHorizontalScrollIndicator={false}
                style={styles.historyList}
              >
                {searchHistory.map((plate, index) => (
                  <TouchableOpacity
                    key={index}
                    style={styles.historyItem}
                    onPress={() => handleSelectFromHistory(plate)}
                  >
                    <Text style={styles.historyText}>{plate}</Text>
                  </TouchableOpacity>
                ))}
              </ScrollView>
            </View>
          )}
        </View>

        {searchResult && (
          <View style={styles.resultSection}>
            <Text style={styles.resultTitle}>
              Kết quả tra cứu: {searchResult.licensePlate}
            </Text>

            {searchResult.totalViolations > 0 ? (
              <View>
                {/* Hiển thị tổng quan vi phạm */}
                <View style={styles.summaryContainer}>
                  <View style={styles.summaryItem}>
                    <Text style={styles.summaryValue}>
                      {searchResult.totalViolations}
                    </Text>
                    <Text style={styles.summaryLabel}>Tổng vi phạm</Text>
                  </View>
                  <View style={styles.summaryItem}>
                    <Text style={[styles.summaryValue, { color: "#E53935" }]}>
                      {searchResult.pendingViolations}
                    </Text>
                    <Text style={styles.summaryLabel}>Chưa xử phạt</Text>
                  </View>
                  <View style={styles.summaryItem}>
                    <Text style={[styles.summaryValue, { color: "#43A047" }]}>
                      {searchResult.resolvedViolations}
                    </Text>
                    <Text style={styles.summaryLabel}>Đã xử phạt</Text>
                  </View>
                </View>

                {/* Hiển thị danh sách vi phạm */}
                {searchResult.violations.map((violation, index) => (
                  <View key={index} style={styles.fineItem}>
                    <View style={styles.fineHeader}>
                      <View style={styles.vehicleInfoContainer}>
                        <Text style={styles.licensePlateText}>
                          {searchResult.licensePlate}
                        </Text>
                        <Text style={styles.vehicleTypeText}>
                          {searchResult.vehicleType || "Xe ô tô"} -{" "}
                          {searchResult.plateColor || "Trắng"}
                        </Text>
                      </View>
                      <View
                        style={[
                          styles.fineStatus,
                          violation.status === "Chưa xử phạt"
                            ? styles.unpaidStatus
                            : styles.paidStatus,
                        ]}
                      >
                        <Text
                          style={{
                            color:
                              violation.status === "Chưa xử phạt"
                                ? "#E53935"
                                : "#43A047",
                            fontWeight: "500",
                          }}
                        >
                          {violation.status || "Chưa xử phạt"}
                        </Text>
                      </View>
                    </View>

                    <View style={styles.fineDetails}>
                      <View style={styles.detailRow}>
                        <MaterialIcons
                          name="access-time"
                          size={16}
                          color="#666"
                        />
                        <Text style={styles.detailText}>
                          Thời gian vi phạm:{" "}
                          {violation.violationTime || "Không có thông tin"}
                        </Text>
                      </View>

                      <View style={styles.detailRow}>
                        <Ionicons
                          name="location-outline"
                          size={16}
                          color="#666"
                        />
                        <Text style={styles.detailText}>
                          Địa điểm: {violation.location || "Không có thông tin"}
                        </Text>
                      </View>

                      <View style={styles.detailRow}>
                        <MaterialIcons
                          name="error-outline"
                          size={16}
                          color="#666"
                        />
                        <Text style={styles.detailText}>
                          Hành vi vi phạm:{" "}
                          {violation.violationType || "Không có thông tin"}
                        </Text>
                      </View>

                      <View style={styles.detailRow}>
                        <Ionicons
                          name="shield-outline"
                          size={16}
                          color="#666"
                        />
                        <Text style={styles.detailText}>
                          Đơn vị phát hiện:{" "}
                          {violation.detectionUnit || "Không có thông tin"}
                        </Text>
                      </View>

                      <View style={styles.detailRow}>
                        <Ionicons
                          name="call-outline"
                          size={16}
                          color="#666"
                        />
                        <Text style={styles.detailText}>
                          Số điện thoại liên hệ:{" "}
                          {violation.phone || "Không có thông tin"}
                        </Text>
                      </View>
                    </View>
                  </View>
                ))}
              </View>
            ) : (
              <View style={styles.noFineContainer}>
                <Image
                  source={require("@/assets/images/check-circle.png")}
                  style={styles.noFineImage}
                />
                <Text style={styles.noFineTitle}>
                  Không tìm thấy thông tin phạt nguội
                </Text>
                <Text style={styles.noFineText}>
                  Biển số xe {searchResult.licensePlate} không có thông tin phạt
                  nguội trong hệ thống
                </Text>
              </View>
            )}
          </View>
        )}

        <View style={styles.infoSection}>
          <Text style={styles.sectionTitle}>Thông tin hữu ích</Text>

          <View style={styles.infoItem}>
            <Text style={styles.infoTitle}>Phạt nguội là gì?</Text>
            <Text style={styles.infoContent}>
              Phạt nguội là hình thức xử phạt vi phạm giao thông được ghi nhận
              qua hệ thống camera giám sát, không có sự tham gia trực tiếp của
              cảnh sát giao thông tại thời điểm vi phạm.
            </Text>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  content: {
    flex: 1,
    marginTop: 50, // Thêm margin top để không bị che bởi nút back
  },
  scrollContent: {
    paddingBottom: 30,
  },
  // Styles cho nút back custom
  backButtonContainer: {
    position: "absolute",
    left: 16,
    right: 16,
    flexDirection: "row",
    alignItems: "center",
    zIndex: 10,
  },
  backButton: {
    backgroundColor: "white",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  screenTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
    marginLeft: 16,
  },
  header: {
    alignItems: "center",
    paddingHorizontal: 20,
    paddingVertical: 30,
    backgroundColor: "#f9f9f9",
  },
  headerImage: {
    width: 120,
    height: 120,
    marginBottom: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 10,
    textAlign: "center",
  },
  description: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
  },
  searchSection: {
    padding: 20,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  searchInputContainer: {
    flexDirection: "row",
    marginBottom: 10,
  },
  searchInput: {
    flex: 1,
    height: 50,
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    paddingHorizontal: 15,
    fontSize: 16,
    backgroundColor: "#fff",
  },
  searchButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: 20,
    justifyContent: "center",
    alignItems: "center",
    borderRadius: 8,
    marginLeft: 10,
    minWidth: 100,
  },
  disabledButton: {
    backgroundColor: "#cccccc",
  },
  searchButtonText: {
    color: "#fff",
    fontWeight: "bold",
    fontSize: 16,
  },
  clearButton: {
    alignSelf: "flex-end",
    padding: 8,
  },
  clearButtonText: {
    color: Colors.primary,
    fontWeight: "500",
  },
  errorText: {
    color: "#E53935",
    marginTop: 10,
    fontSize: 14,
    textAlign: "center",
  },
  historySection: {
    marginTop: 20,
  },
  historyTitle: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 10,
    color: "#666",
  },
  historyList: {
    flexDirection: "row",
    marginBottom: 10,
  },
  historyItem: {
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    marginRight: 10,
  },
  historyText: {
    fontSize: 14,
    color: "#333",
  },
  resultSection: {
    padding: 20,
    backgroundColor: "#f9f9f9",
  },
  resultTitle: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 15,
  },
  // Styles cho tổng quan vi phạm
  summaryContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryItem: {
    alignItems: "center",
    flex: 1,
  },
  summaryValue: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  summaryLabel: {
    fontSize: 14,
    color: "#666",
    marginTop: 5,
  },
  // Styles cho item vi phạm
  fineItem: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 15,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  fineHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 10,
    paddingBottom: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  vehicleInfoContainer: {
    flex: 1,
  },
  licensePlateText: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 4,
  },
  vehicleTypeText: {
    fontSize: 14,
    color: "#666",
  },
  fineDetails: {
    gap: 10,
  },
  detailRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  detailText: {
    fontSize: 14,
    color: "#333",
    flex: 1,
  },
  fineStatus: {
    paddingHorizontal: 10,
    paddingVertical: 5,
    borderRadius: 15,
    alignSelf: "flex-start",
  },
  unpaidStatus: {
    backgroundColor: "#FFEBEE",
  },
  paidStatus: {
    backgroundColor: "#E8F5E9",
  },
  // Styles cho nơi giải quyết vi phạm
  resolutionSection: {
    marginTop: 10,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: "#eee",
  },
  resolutionTitle: {
    fontSize: 14,
    fontWeight: "bold",
    marginBottom: 8,
  },
  resolutionPlace: {
    backgroundColor: "#f9f9f9",
    borderRadius: 8,
    padding: 10,
    marginBottom: 8,
  },
  resolutionName: {
    fontSize: 14,
    fontWeight: "500",
    marginBottom: 5,
  },
  resolutionDetail: {
    fontSize: 13,
    color: "#666",
    flex: 1,
  },
  // Styles cho nút thanh toán
  paymentButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 10,
  },
  paymentButtonText: {
    color: "#000",
    fontSize: 16,
    fontWeight: "bold",
  },
  // Styles cho trường hợp không có vi phạm
  noFineContainer: {
    backgroundColor: "#fff",
    borderRadius: 10,
    padding: 20,
    alignItems: "center",
  },
  noFineImage: {
    width: 60,
    height: 60,
    marginBottom: 16,
    tintColor: "#4CAF50",
  },
  noFineTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 8,
    textAlign: "center",
  },
  noFineText: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    lineHeight: 22,
  },
  infoSection: {
    padding: 20,
  },
  infoItem: {
    marginBottom: 20,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 8,
  },
  infoContent: {
    fontSize: 14,
    color: "#666",
    lineHeight: 20,
  },
});
