import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  TextInput,
  SafeAreaView,
  StatusBar,
} from "react-native";
import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { Colors } from "@/constants/Colors";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { updatePromoCode } from "@/store/slices/serviceSlice";

export default function PromotionScreen() {
  const dispatch = useAppDispatch();
  const savedPromoCode = useAppSelector((state) => state.service.promoCode);

  const [promoCode, setPromoCode] = useState(savedPromoCode);
  const insets = useSafeAreaInsets();

  // Cập nhật state local khi Redux state thay đổi
  useEffect(() => {
    setPromoCode(savedPromoCode);
  }, [savedPromoCode]);

  const handleBack = () => {
    router.back();
  };

  const handleApplyPromo = () => {
    const promotionCode = promoCode.trim();

    // Lưu mã khuyến mãi vào Redux store
    dispatch(updatePromoCode(promotionCode));

    // Xóa text trong input
    setPromoCode("");

    // Quay lại màn hình trước đó
    setTimeout(() => {
      router.back();
    }, 300);
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="dark-content" />

      {/* Header */}
      <View style={[styles.header, { marginTop: insets.top > 0 ? 0 : 10 }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Ưu đãi</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Promo Code Input */}
      <View style={styles.promoInputContainer}>
        <View style={styles.promoInputWrapper}>
          <Ionicons
            name="pricetag-outline"
            size={20}
            color="#999"
            style={styles.promoIcon}
          />
          <TextInput
            style={styles.promoInput}
            placeholder="Thêm mã khuyến mãi"
            value={promoCode}
            onChangeText={setPromoCode}
          />
        </View>
        <TouchableOpacity
          style={[
            styles.applyButton,
            promoCode.trim()
              ? styles.applyButtonActive
              : styles.applyButtonInactive,
          ]}
          onPress={handleApplyPromo}
          disabled={!promoCode.trim()}
        >
          <Text
            style={[
              styles.applyButtonText,
              promoCode.trim()
                ? styles.applyButtonTextActive
                : styles.applyButtonTextInactive,
            ]}
          >
            Áp dụng
          </Text>
        </TouchableOpacity>
      </View>

      {/* Available Promotions */}
      <View style={styles.availablePromosSection}>
        <Text style={styles.sectionTitle}>Ưu đãi khả dụng</Text>

        {/* Danh sách ưu đãi sẽ hiển thị ở đây */}
        {/* Hiện tại để trống như trong hình */}
      </View>

      {/* Home Indicator Space */}
      <View style={{ height: insets.bottom }} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    height: 56,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  promoInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 16,
    marginTop: 16,
    marginBottom: 24,
  },
  promoInputWrapper: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E0E0E0",
    borderRadius: 30,
    paddingHorizontal: 16,
    height: 48,
    marginRight: 12,
  },
  promoIcon: {
    marginRight: 8,
  },
  promoInput: {
    flex: 1,
    height: 48,
    fontSize: 16,
    color: "#555",
  },
  applyButton: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    justifyContent: "center",
    alignItems: "center",
  },
  applyButtonActive: {
    backgroundColor: Colors.primary,
  },
  applyButtonInactive: {
    backgroundColor: "#F5F5F5",
  },
  applyButtonText: {
    fontSize: 14,
    fontWeight: "600",
  },
  applyButtonTextActive: {
    color: "#333",
  },
  applyButtonTextInactive: {
    color: "#999",
  },
  availablePromosSection: {
    paddingHorizontal: 16,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 16,
  },
});
