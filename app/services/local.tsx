import { getDirections, RouteInfo } from "@/api/direction.api";
import { getCoordinatesFromPlaceId } from "@/api/geocoding.api";
import { LocationSuggestion, searchLocations } from "@/api/location.api";
import { PaymentMethod } from "@/api/payment-methods.api";
//import GoongConstants from "@/constants/GoongConstants"; // Không cần nữa vì đã di chuyển vào RouteMapView
import LocationSearchOverlay from "@/components/LocationSearchOverlay";
import RouteMapView from "@/components/RouteMapView";
import { useDebounce } from "@/hooks/useDebounce";
import { useAppSelector } from "@/store/hooks";
import { Ionicons, MaterialIcons } from "@expo/vector-icons";
import { router, useLocalSearchParams } from "expo-router";
import { useCallback, useEffect, useRef, useState } from "react";
import {
  ActivityIndicator,
  Alert,
  Image,
  StyleSheet,
  Text,
  TextInput,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

// Define interfaces for route data
interface LocationPoint {
  place_id: string;
  description: string;
}

interface RouteData {
  pickup: LocationPoint;
  destination: LocationPoint;
  stopPoints: LocationPoint[];
}

export default function App() {
  // Get route data from params
  const params = useLocalSearchParams<{
    routeData: string;
    selectedPaymentMethod?: string;
  }>();

  // Không cần khai báo loadMap nữa vì đã di chuyển vào RouteMapView

  // State for loading status
  const [isLoading, setIsLoading] = useState(true);
  const [isSearchLoading, setIsSearchLoading] = useState(false);

  // Define map points with proper format (longitude, latitude)
  const [mapPoints, setMapPoints] = useState<
    {
      id: string;
      coordinates: [number, number];
      title: string;
    }[]
  >([]);

  // State for route information
  const [routeInfo, setRouteInfo] = useState<RouteInfo | null>(null);

  // State for billing information
  const [baseFee, setBaseFee] = useState(15000); // Base fee in VND
  const [distanceFee, setDistanceFee] = useState(0); // Distance-based fee
  const [totalPrice, setTotalPrice] = useState(0); // Total price
  const [showBilling, setShowBilling] = useState(false); // Whether to show billing info

  // Sử dụng Redux để lấy ghi chú và mã quảng cáo
  const { note, promoCode } = useAppSelector((state) => state.service);

  // State for order details
  const [showConfirmModal, setShowConfirmModal] = useState(false); // Whether to show confirmation modal
  const [discountAmount, setDiscountAmount] = useState(0); // Discount amount from promo code

  // Payment method state
  const [paymentMethod, setPaymentMethod] = useState("CASH"); // Default to CASH
  const [paymentMethods, setPaymentMethods] = useState<PaymentMethod[]>([]);
  const [isPaymentMethodsLoading, setIsPaymentMethodsLoading] = useState(false);

  // Fetch payment methods from API
  const fetchPaymentMethods = useCallback(async () => {
    // Skip fetching if we already have payment methods
    if (paymentMethods.length > 0) {
      return;
    }

    setIsPaymentMethodsLoading(true);
    try {
      // Import directly to avoid dynamic import issues
      const { getPaymentMethods } = require("@/api/payment-methods.api");
      const methods = await getPaymentMethods();
      setPaymentMethods(methods);
    } catch (error) {
      console.error("Error fetching payment methods:", error);
    } finally {
      setIsPaymentMethodsLoading(false);
    }
  }, [paymentMethods.length]);

  // Fetch payment methods when component mounts
  useEffect(() => {
    fetchPaymentMethods();
  }, [fetchPaymentMethods]);

  // Xử lý khi nhận được phương thức thanh toán từ màn hình chọn
  useEffect(() => {
    if (params.selectedPaymentMethod) {
      setPaymentMethod(params.selectedPaymentMethod as string);
    }
  }, [params.selectedPaymentMethod]);

  // State for location search and selection
  const [searchQuery, setSearchQuery] = useState("");
  const [suggestions, setSuggestions] = useState<LocationSuggestion[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [activeInput, setActiveInput] = useState<
    "pickup" | "destination" | null
  >(null);

  // State for pickup and destination
  const [pickupAddress, setPickupAddress] = useState("");
  const [destinationAddress, setDestinationAddress] = useState("");
  const [selectedPickup, setSelectedPickup] = useState<LocationPoint | null>(
    null
  );
  const [selectedDestination, setSelectedDestination] =
    useState<LocationPoint | null>(null);
  const [coordinates, setCoordinates] = useState<[number, number]>([
    107.8091190568345, 11.54498921967426,
  ]);

  // Debounce search query to avoid too many API calls
  const debouncedSearchQuery = useDebounce(searchQuery, 500);

  // Refs for input fields
  const searchInputRef = useRef<TextInput>(null);
  const insets = useSafeAreaInsets();

  // Calculate delivery fee based on distance
  const calculateDeliveryFee = useCallback((distance: number) => {
    // Base fee for first 2km
    let fee = 15000;

    // Additional fee for each km after first 2km
    if (distance > 2000) {
      const additionalDistance = distance - 2000;
      const additionalKm = Math.ceil(additionalDistance / 1000);
      fee += additionalKm * 5000;
    }

    return fee;
  }, []);

  // Apply promo code
  const applyPromoCode = useCallback((code: string, originalPrice: number) => {
    // Simple promo code logic - in a real app, this would validate against a backend
    if (code === "SAYGO50") {
      return Math.round(originalPrice * 0.5); // 50% discount
    } else if (code === "SAYGO20") {
      return Math.round(originalPrice * 0.2); // 20% discount
    } else if (code === "SAYGO10") {
      return Math.round(originalPrice * 0.1); // 10% discount
    }
    return 0; // No discount
  }, []);

  // Handle search for locations
  useEffect(() => {
    const searchForLocations = async () => {
      if (debouncedSearchQuery.length >= 5) {
        try {
          setIsSearchLoading(true);
          const results = await searchLocations(debouncedSearchQuery);
          setSuggestions(results);
        } catch (error) {
          console.error("Error searching for locations:", error);
        } finally {
          setIsSearchLoading(false);
        }
      } else {
        setSuggestions([]);
      }
    };

    searchForLocations();
  }, [debouncedSearchQuery]);

  // Handle location selection
  const handleSelectLocation = useCallback(
    async (location: LocationSuggestion) => {
      const newLocation: LocationPoint = {
        place_id: location.place_id,
        description: location.description,
      };

      let newPickup = selectedPickup;
      let newDestination = selectedDestination;

      if (activeInput === "pickup") {
        setPickupAddress(location.description);
        setSelectedPickup(newLocation);
        newPickup = newLocation;
      } else if (activeInput === "destination") {
        setDestinationAddress(location.description);
        setSelectedDestination(newLocation);
        newDestination = newLocation;
      }

      setSearchQuery("");
      setSuggestions([]);
      setShowSuggestions(false);
      setActiveInput(null);

      if (newPickup && newDestination) {
        try {
          setIsLoading(true);

          const pickupCoords = await getCoordinatesFromPlaceId(
            newPickup.place_id
          );
          const destCoords = await getCoordinatesFromPlaceId(
            newDestination.place_id
          );

          if (!pickupCoords || !destCoords) {
            Alert.alert(
              "Lỗi",
              "Không thể lấy tọa độ cho điểm đón hoặc điểm đến"
            );
            setIsLoading(false);
            return;
          }

          const points = [
            {
              id: "pickup",
              coordinates: [pickupCoords.lng, pickupCoords.lat] as [
                number,
                number
              ],
              title: newPickup.description,
            },
            {
              id: "destination",
              coordinates: [destCoords.lng, destCoords.lat] as [number, number],
              title: newDestination.description,
            },
          ];

          // Calculate midpoint between pickup and destination
          const midLng = (pickupCoords.lng + destCoords.lng) / 2;
          const midLat = (pickupCoords.lat + destCoords.lat) / 2;

          setCoordinates([midLng, midLat]);

          const directions = await getDirections(
            [pickupCoords.lat, pickupCoords.lng],
            [destCoords.lat, destCoords.lng],
            "car"
          );

          if (directions) {
            setRouteInfo(directions);

            setMapPoints(points);

            // Calculate fees
            const baseFeeValue = 15000; // Base service fee
            const distanceFeeValue = calculateDeliveryFee(
              directions.distance.value
            );
            const totalPriceValue = baseFeeValue + distanceFeeValue;
            const discountValue = applyPromoCode(promoCode, totalPriceValue);

            // Update fee state
            setBaseFee(baseFeeValue);
            setDistanceFee(distanceFeeValue);
            setTotalPrice(totalPriceValue);
            setDiscountAmount(discountValue);
            setShowBilling(true);
          } else {
            // If directions API fails, fallback to straight line
            setMapPoints(points);
            setShowBilling(false);
            Alert.alert(
              "Thông báo",
              "Không thể lấy thông tin tuyến đường. Hiển thị đường thẳng."
            );
          }
        } catch (error) {
          console.error("Error updating map with route:", error);
          Alert.alert("Lỗi", "Đã xảy ra lỗi khi cập nhật tuyến đường");
        } finally {
          setIsLoading(false);
        }
      }
    },
    [
      activeInput,
      selectedPickup,
      selectedDestination,
      calculateDeliveryFee,
      applyPromoCode,
      promoCode,
    ]
  );

  // Load route data from params if available
  useEffect(() => {
    const fetchCoordinates = async () => {
      try {
        setIsLoading(true);

        if (!params.routeData) {
          setIsLoading(false);
          return;
        }

        // Parse route data
        const routeData: RouteData = JSON.parse(
          decodeURIComponent(params.routeData)
        );

        // Set pickup and destination
        setPickupAddress(routeData.pickup.description);
        setDestinationAddress(routeData.destination.description);
        setSelectedPickup(routeData.pickup);
        setSelectedDestination(routeData.destination);

        // Get coordinates for pickup point
        const pickupCoords = await getCoordinatesFromPlaceId(
          routeData.pickup.place_id
        );

        // Get coordinates for destination point
        const destCoords = await getCoordinatesFromPlaceId(
          routeData.destination.place_id
        );

        if (!pickupCoords || !destCoords) {
          Alert.alert("Lỗi", "Không thể lấy tọa độ cho điểm đón hoặc điểm đến");
          setIsLoading(false);
          return;
        }

        // Create points array for markers
        const points = [
          {
            id: "pickup",
            coordinates: [pickupCoords.lng, pickupCoords.lat] as [
              number,
              number
            ],
            title: routeData.pickup.description,
          },
          {
            id: "destination",
            coordinates: [destCoords.lng, destCoords.lat] as [number, number],
            title: routeData.destination.description,
          },
        ];

        // Calculate midpoint between pickup and destination
        const midLng = (pickupCoords.lng + destCoords.lng) / 2;
        const midLat = (pickupCoords.lat + destCoords.lat) / 2;

        // Set center coordinates to midpoint
        setCoordinates([midLng, midLat]);

        // Get directions from Goong API
        const directions = await getDirections(
          [pickupCoords.lat, pickupCoords.lng],
          [destCoords.lat, destCoords.lng],
          "car"
        );

        if (directions) {
          // Store route information
          setRouteInfo(directions);

          // Set map points
          setMapPoints(points);

          // Calculate fees
          const baseFeeValue = 15000; // Base service fee
          const distanceFeeValue = calculateDeliveryFee(
            directions.distance.value
          );
          const totalPriceValue = baseFeeValue + distanceFeeValue;
          const discountValue = applyPromoCode(promoCode, totalPriceValue);

          // Update fee state
          setBaseFee(baseFeeValue);
          setDistanceFee(distanceFeeValue);
          setTotalPrice(totalPriceValue);
          setDiscountAmount(discountValue);
          setShowBilling(true);
        } else {
          // If directions API fails, fallback to straight line
          setMapPoints(points);
          setShowBilling(false);
          Alert.alert(
            "Thông báo",
            "Không thể lấy thông tin tuyến đường. Hiển thị đường thẳng."
          );
        }
      } catch (error) {
        console.error("Error fetching coordinates:", error);
        Alert.alert("Lỗi", "Đã xảy ra lỗi khi tải dữ liệu bản đồ");
      } finally {
        setIsLoading(false);
      }
    };

    fetchCoordinates();
  }, [params.routeData, calculateDeliveryFee, applyPromoCode, promoCode]);

  const handleBack = () => {
    router.back();
  };

  return (
    <View style={{ flex: 1 }}>
      {isLoading ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Đang tải bản đồ...</Text>
        </View>
      ) : (
        <>
          <RouteMapView
            routeInfo={routeInfo}
            mapPoints={mapPoints}
            coordinates={coordinates}
          />

          {/* Bottom container */}
          <View style={styles.bottomContainer}>
            {/* Action buttons */}
            <View style={styles.actionButtonsContainer}>
              <View style={styles.leftButtonsGroup}>
                {/* Phương thức thanh toán */}
                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() =>
                    router.push({
                      pathname: "/payment-method-selection",
                      params: { currentPaymentMethod: paymentMethod },
                    })
                  }
                >
                  {(() => {
                    const method = paymentMethods.find(
                      (m: PaymentMethod) => m.code === paymentMethod
                    );

                    if (!method) {
                      // Show skeleton loading or default if payment methods are not loaded yet
                      return isPaymentMethodsLoading ? (
                        <View style={styles.paymentMethodButtonContent}>
                          <View style={styles.skeletonIconContainer}>
                            <View style={styles.skeletonIcon} />
                          </View>
                          <View style={styles.skeletonTextContainer}>
                            <View style={styles.skeletonText} />
                          </View>
                        </View>
                      ) : (
                        <Text style={styles.actionButtonText}>Tiền mặt</Text>
                      );
                    }

                    return (
                      <View style={styles.paymentMethodButtonContent}>
                        {/* Display payment method icon from URL */}
                        <View style={styles.paymentMethodIconContainer}>
                          <Image
                            source={{ uri: method.icon }}
                            style={styles.paymentMethodIcon}
                            resizeMode="contain"
                          />
                        </View>
                        <Text style={styles.actionButtonText}>
                          {method.name}
                        </Text>
                      </View>
                    );
                  })()}
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => router.push("/services/local-promotion")}
                >
                  <Text style={styles.actionButtonText}>
                    Ưu đãi{promoCode ? ` (${promoCode})` : ""}
                  </Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.actionButton}
                  onPress={() => router.push("/services/local-notes")}
                >
                  <Text style={styles.actionButtonText}>
                    Ghi chú{note ? " (✓)" : ""}
                  </Text>
                </TouchableOpacity>
              </View>

              <TouchableOpacity
                style={[styles.actionButton, styles.iconButton]}
                onPress={() => router.push("/services/local-about")}
              >
                <Ionicons name="ellipsis-horizontal" size={18} color="#333" />
              </TouchableOpacity>
            </View>

            {/* Order button with price */}
            <TouchableOpacity
              style={styles.findDriverButton}
              onPress={() => setShowConfirmModal(true)}
            >
              <View style={styles.priceContainer}>
                <Text style={styles.originalPrice}>976.000đ</Text>
                <Text style={styles.discountedPrice}>925.000đ</Text>
              </View>
              <View style={styles.buttonTextContainer}>
                <Text style={styles.buttonText}>THUÊ TÀI XẾ </Text>
                <MaterialIcons name="arrow-forward" size={20} color="#333" />
              </View>
            </TouchableOpacity>
          </View>

          {/* Location search container */}
          <View style={[styles.searchContainer, { top: insets.top + 10 }]}>
            <TouchableOpacity
              style={[
                styles.locationInputContainer,
                { borderBottomWidth: 1, borderBottomColor: "#f0f0f0" },
              ]}
              onPress={() => {
                setActiveInput("pickup");
                setShowSuggestions(true);
                setSearchQuery(pickupAddress);
                setTimeout(() => {
                  searchInputRef.current?.focus();
                }, 100);
              }}
            >
              <View style={styles.locationIconContainer}>
                <Ionicons name="location" size={20} color="#ffde59" />
              </View>
              <View style={styles.locationTextContainer}>
                <Text style={styles.locationLabel}>Điểm đón</Text>
                {pickupAddress ? (
                  <Text style={styles.locationText} numberOfLines={2}>
                    {pickupAddress}
                  </Text>
                ) : (
                  <Text style={styles.locationPlaceholder}>Chọn điểm đón</Text>
                )}
              </View>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.locationInputContainer}
              onPress={() => {
                setActiveInput("destination");
                setShowSuggestions(true);
                setSearchQuery(destinationAddress);
                setTimeout(() => {
                  searchInputRef.current?.focus();
                }, 100);
              }}
            >
              <View style={styles.locationIconContainer}>
                <Ionicons name="flag" size={20} color="#2E64FE" />
              </View>
              <View style={styles.locationTextContainer}>
                <Text style={styles.locationLabel}>Điểm đến</Text>
                {destinationAddress ? (
                  <Text style={styles.locationText} numberOfLines={2}>
                    {destinationAddress}
                  </Text>
                ) : (
                  <Text style={styles.locationPlaceholder}>Chọn điểm đến</Text>
                )}
              </View>
            </TouchableOpacity>
          </View>

          {/* Back button */}
          <TouchableOpacity
            style={[styles.backButton, { top: insets.top + 10 }]}
            onPress={handleBack}
          >
            <Ionicons name="arrow-back" size={24} color="black" />
          </TouchableOpacity>

          {/* Search overlay */}
          <LocationSearchOverlay
            showSuggestions={showSuggestions}
            searchQuery={searchQuery}
            setSearchQuery={setSearchQuery}
            suggestions={suggestions}
            setSuggestions={setSuggestions}
            setShowSuggestions={setShowSuggestions}
            isSearchLoading={isSearchLoading}
            activeInput={activeInput}
            searchInputRef={searchInputRef}
            handleSelectLocation={handleSelectLocation}
          />

          {/* Modal xác nhận đặt xe */}
          {showConfirmModal && (
            <View style={styles.modalOverlay}>
              <View style={styles.modalContent}>
                <View style={styles.modalHeader}>
                  <Text style={styles.modalTitle}>Xác nhận đặt xe</Text>
                  <TouchableOpacity onPress={() => setShowConfirmModal(false)}>
                    <Ionicons name="close" size={24} color="#333" />
                  </TouchableOpacity>
                </View>
                <View style={styles.modalBody}>
                  <Text style={styles.sectionTitle}>Thông tin chuyến đi</Text>

                  {pickupAddress && (
                    <View style={styles.locationItem}>
                      <Ionicons name="location" size={20} color="#ffde59" style={{ marginRight: 12 }} />
                      <Text style={{ flex: 1, fontSize: 14, color: "#333" }}>{pickupAddress}</Text>
                    </View>
                  )}

                  {destinationAddress && (
                    <View style={styles.locationItem}>
                      <Ionicons name="flag" size={20} color="#2E64FE" style={{ marginRight: 12 }} />
                      <Text style={{ flex: 1, fontSize: 14, color: "#333" }}>{destinationAddress}</Text>
                    </View>
                  )}

                  {routeInfo && (
                    <View style={styles.routeDetails}>
                      <Text style={{ fontSize: 14, color: "#666" }}>Khoảng cách: {routeInfo.distance.text}</Text>
                      <Text style={{ fontSize: 14, color: "#666" }}>Thời gian: {routeInfo.duration.text}</Text>
                    </View>
                  )}

                  {promoCode && (
                    <View style={{ marginTop: 12 }}>
                      <Text style={{ fontSize: 14, color: "#4CAF50" }}>Mã ưu đãi: {promoCode}</Text>
                    </View>
                  )}

                  {note && (
                    <View style={{ marginTop: 12 }}>
                      <Text style={{ fontSize: 14, color: "#666" }}>Ghi chú: {note}</Text>
                    </View>
                  )}
                </View>
                <View style={styles.modalFooter}>
                  <TouchableOpacity
                    style={styles.cancelButton}
                    onPress={() => setShowConfirmModal(false)}
                  >
                    <Text style={styles.cancelButtonText}>Hủy</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.confirmButton}
                    onPress={() => {
                      setShowConfirmModal(false);
                      Alert.alert("Thành công", "Đã đặt xe thành công!");
                    }}
                  >
                    <Text style={styles.confirmButtonText}>Xác nhận</Text>
                  </TouchableOpacity>
                </View>
              </View>
            </View>
          )}
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  backButton: {
    position: "absolute",
    left: 16,
    backgroundColor: "white",
    borderRadius: 20,
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 15, // Increased z-index to ensure it's above other elements
  },
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: "#333",
  },
  // Billing container styles
  billingContainer: {
    position: "absolute",
    bottom: 20,
    left: 16,
    right: 16,
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 10,
    overflow: "hidden",
  },
  billingHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "#f9f9f9",
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  routeInfoRow: {
    flexDirection: "row",
    alignItems: "center",
  },
  billingDetails: {
    padding: 16,
  },
  feeRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 8,
  },
  feeLabel: {
    fontSize: 14,
    color: "#666",
  },
  feeValue: {
    fontSize: 14,
    color: "#333",
  },
  discountText: {
    color: "#4CAF50",
  },
  divider: {
    height: 1,
    backgroundColor: "#eee",
    marginVertical: 8,
  },
  totalRow: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 4,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
  },
  totalValue: {
    fontSize: 18,
    fontWeight: "700",
    color: "#2E64FE",
  },
  orderButton: {
    backgroundColor: "#2E64FE",
    borderRadius: 8,
    paddingVertical: 12,
    marginTop: 16,
    alignItems: "center",
  },
  orderButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  modalContent: {
    backgroundColor: "white",
    borderTopLeftRadius: 16,
    borderTopRightRadius: 16,
    maxHeight: "80%",
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#eee",
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  modalBody: {
    padding: 16,
    maxHeight: "60%",
  },
  modalSection: {
    marginBottom: 20,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#333",
    marginBottom: 12,
  },
  locationItem: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 12,
  },
  routeDetails: {
    flexDirection: "row",
    justifyContent: "space-between",
    backgroundColor: "#f9f9f9",
    padding: 12,
    borderRadius: 8,
    marginTop: 8,
  },
  promoInputContainer: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    overflow: "hidden",
  },
  promoInput: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
  },
  noteInputContainer: {
    borderWidth: 1,
    borderColor: "#ddd",
    borderRadius: 8,
    overflow: "hidden",
  },
  noteInput: {
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 14,
    textAlignVertical: "top",
    minHeight: 80,
  },
  priceSummary: {
    backgroundColor: "#f9f9f9",
    padding: 12,
    borderRadius: 8,
  },
  modalFooter: {
    flexDirection: "row",
    padding: 16,
    borderTopWidth: 1,
    borderTopColor: "#eee",
  },
  cancelButton: {
    flex: 1,
    backgroundColor: "#f5f5f5",
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
    marginRight: 8,
  },
  cancelButtonText: {
    color: "#666",
    fontSize: 16,
    fontWeight: "600",
  },
  confirmButton: {
    flex: 1,
    backgroundColor: "#2E64FE",
    borderRadius: 8,
    paddingVertical: 12,
    alignItems: "center",
    marginLeft: 8,
  },
  confirmButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "600",
  },
  // Search container styles
  searchContainer: {
    position: "absolute",
    top: 60,
    left: 70, // Increased left margin to avoid overlapping with back button
    right: 16,
    backgroundColor: "white",
    borderRadius: 8,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 5,
    paddingHorizontal: 8,
    paddingVertical: 2,
  },
  locationInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
  },
  locationIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "#f5f5f5",
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    color: "#666",
    marginBottom: 2,
  },
  locationText: {
    fontSize: 14,
    color: "#333",
    fontWeight: "500",
  },
  locationPlaceholder: {
    fontSize: 14,
    color: "#999",
  },
  // Search overlay styles moved to LocationSearchOverlay component
  driverMarker: {
    backgroundColor: "white",
    borderRadius: 20,
    padding: 8,
    borderWidth: 2,
    borderColor: "#FFE66D",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 3,
    elevation: 5,
  },
  routeInfoContainer: {
    position: "absolute",
    bottom: 20,
    alignSelf: "center",
    backgroundColor: "white",
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  routeInfoText: {
    fontSize: 14,
    fontWeight: "600",
    color: "#333",
  },
  // Bottom container styles
  bottomContainer: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "#FFF8F3",
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 16,
    paddingBottom: 24,
    zIndex: 10,
  },
  // Trip info styles
  tripInfoContainer: {
    flexDirection: "row",
    backgroundColor: "white",
    borderRadius: 12,
    marginBottom: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  tripInfoItem: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: "center",
  },
  tripInfoLabel: {
    fontSize: 14,
    color: "#666",
    marginBottom: 4,
  },
  tripInfoValue: {
    fontSize: 16,
    fontWeight: "500",
    color: "#333",
  },
  tripInfoDivider: {
    width: 1,
    backgroundColor: "#eee",
  },
  // Action buttons styles
  actionButtonsContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 12,
  },
  leftButtonsGroup: {
    flexDirection: "row",
  },
  actionButton: {
    backgroundColor: "white",
    borderRadius: 10,
    paddingVertical: 8,
    paddingHorizontal: 16,
    marginRight: 12,
    borderWidth: 1,
    borderColor: "#E0E0E0",
    shadowRadius: 2,
    elevation: 2,
    justifyContent: "center",
    alignItems: "center",
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: "500",
    color: "#333",
    textAlign: "center",
  },
  iconButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 0,
    marginRight: 0,
    backgroundColor: "white",
    borderWidth: 1,
    borderColor: "#E0E0E0",
  },
  // Bottom price container styles
  bottomPriceContainer: {
    position: "absolute",
    bottom: 20,
    left: 16,
    right: 16,
    backgroundColor: "white",
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 10,
  },
  findDriverButton: {
    backgroundColor: "#ffde59", // Yellow color as in the image
    borderRadius: 30,
    paddingVertical: 12,
    paddingHorizontal: 16,
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  priceContainer: {
    flexDirection: "column",
  },
  originalPrice: {
    fontSize: 14,
    color: "#999",
    textDecorationLine: "line-through",
  },
  discountedPrice: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
  },
  buttonTextContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  buttonText: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginRight: 8,
  },
  // Styles for payment method button
  paymentMethodButtonContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  paymentMethodButtonIcon: {
    marginRight: 4,
  },
  // Skeleton styles for payment methods
  skeletonIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: "#f5f5f5",
    marginRight: 8,
    overflow: "hidden",
  },
  skeletonIcon: {
    width: "100%",
    height: "100%",
    backgroundColor: "#e0e0e0",
  },
  skeletonTextContainer: {
    flex: 1,
    height: 16,
    justifyContent: "center",
  },
  skeletonText: {
    height: 14,
    backgroundColor: "#e0e0e0",
    borderRadius: 4,
    width: "70%",
  },
  // Payment method icon from API
  paymentMethodIconContainer: {
    width: 24,
    height: 24,
    borderRadius: 12,
    marginRight: 8,
    justifyContent: "center",
    alignItems: "center",
    overflow: "hidden",
  },
  paymentMethodIcon: {
    width: "100%",
    height: "100%",
  },
});
