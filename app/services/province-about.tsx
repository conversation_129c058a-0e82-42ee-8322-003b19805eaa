import { Ionicons } from "@expo/vector-icons";
import { router } from "expo-router";
import React, { useEffect } from "react";
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { useAppDispatch } from "@/store/hooks";
import {
  fetchAllSettings,
  setDefaultSettings,
} from "@/store/slices/settingsSlice";
import useSettings from "@/hooks/useSettings";

export default function MoreOptionsScreen() {
  const insets = useSafeAreaInsets();
  const dispatch = useAppDispatch();
  const { getSetting } = useSettings({ fetchOnMount: true });

  // Fetch settings on component mount
  useEffect(() => {
    // Try to fetch settings from API
    dispatch(fetchAllSettings())
      .unwrap()
      .catch(() => {
        // If API call fails, use default settings
        dispatch(setDefaultSettings());
      });
  }, [dispatch]);

  const handleBack = () => {
    router.back();
  };

  // Pricing data from Redux or fallback to default values
  const carServicePricing = {
    dayFee: getSetting("saygo_suburban_day_fee", 800000),
    holidayFee: getSetting("saygo_suburban_holiday_fee", 100000),
    serviceFee: getSetting("saygo_suburban_service_fee", 30000),
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar hidden />

      {/* Header */}
      <View style={[styles.header, { marginTop: insets.top > 0 ? 0 : 10 }]}>
        <TouchableOpacity style={styles.backButton} onPress={handleBack}>
          <Ionicons name="arrow-back" size={24} color="#333" />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Thông tin</Text>
        <View style={{ width: 40 }} />
      </View>

      {/* Service Information */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.serviceInfoContainer}>
          <Text style={styles.serviceTitle}>DỊCH VỤ THUÊ TÀI XẾ ĐI TỈNH</Text>
          <Text style={styles.serviceDescription}>
            Dịch vụ tài xế xe ô tô đi tỉnh là giải pháp giúp khách hàng thuê tài
            xế chuyên nghiệp để điều khiển xe cá nhân của chính khách hàng trong
            các chuyến đi đường dài, liên tỉnh, theo lịch trình mong muốn.
          </Text>

          <View style={styles.pricingContainer}>
            <Text style={styles.pricingTitle}>
              Giá dịch vụ = (Giá mỗi ngày × Số ngày đi) + Phí dịch vụ + Phụ phí
              lễ Tết (nếu có)
            </Text>

            <View style={styles.pricingItem}>
              <Text style={styles.pricingText}>
                • Giá mỗi ngày: {carServicePricing.dayFee.toLocaleString()} VNĐ
              </Text>
            </View>

            <View style={styles.pricingItem}>
              <Text style={styles.pricingText}>
                • Phí lễ tết: {carServicePricing.holidayFee.toLocaleString()}{" "}
                VNĐ
              </Text>
            </View>

            <View style={styles.pricingItem}>
              <Text style={styles.pricingText}>
                • Phí dịch vụ: {carServicePricing.serviceFee.toLocaleString()}{" "}
                VNĐ
              </Text>
            </View>
          </View>

          <View style={styles.additionalInfoContainer}>
            <Text style={styles.additionalInfoText}>
              Với mỗi chuyến đi có phí dịch vụ là{" "}
              {carServicePricing.serviceFee.toLocaleString()}đ khoản phí dành
              cho Google map và phí công nghệ cùng các dịch vụ tiện ích đi kèm
            </Text>
          </View>
        </View>
      </ScrollView>

      {/* Home Indicator Space */}
      <View style={{ height: insets.bottom }} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  header: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    paddingHorizontal: 16,
    height: 56,
  },
  backButton: {
    width: 40,
    height: 40,
    justifyContent: "center",
    alignItems: "center",
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#333",
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  serviceInfoContainer: {
    paddingVertical: 20,
  },
  serviceTitle: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#333",
    textAlign: "center",
    marginBottom: 20,
  },
  serviceDescription: {
    fontSize: 16,
    color: "#333",
    marginBottom: 15,
    lineHeight: 22,
  },
  pricingContainer: {
    marginTop: 10,
    marginBottom: 20,
  },
  pricingTitle: {
    fontSize: 16,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 15,
  },
  pricingItem: {
    marginBottom: 10,
  },
  pricingText: {
    fontSize: 16,
    color: "#333",
    lineHeight: 22,
  },
  additionalInfoContainer: {
    marginTop: 10,
  },
  additionalInfoText: {
    fontSize: 16,
    color: "#333",
    marginBottom: 15,
    lineHeight: 22,
  },
  versionContainer: {
    padding: 16,
    alignItems: "center",
  },
  versionText: {
    fontSize: 14,
    color: "#999",
  },
});
