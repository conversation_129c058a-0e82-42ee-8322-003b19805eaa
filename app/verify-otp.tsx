import React, { useState, useRef, useEffect } from "react";
import {
  View,
  StyleSheet,
  Image,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  TextInput,
} from "react-native";
import { Stack, router, useLocalSearchParams } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { ThemedText } from "@/components/ThemedText";
import { Button } from "@/components/ui/Button";
import { Colors } from "@/constants/Colors";
import { OtpPurpose, useAuth } from "@/contexts/AuthContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { MaterialIcons } from "@expo/vector-icons";

export default function VerifyOTPScreen() {
  const { verifyOTP } = useAuth();
  const params = useLocalSearchParams<{ phone: string }>();
  const phone = params.phone || "";

  const [otp, setOtp] = useState(["", "", "", "", "", ""]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");
  const [countdown, setCountdown] = useState(0);
  const inputRefs = useRef<(TextInput | null)[]>([]);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Tự động focus vào ô đầu tiên khi màn hình được tải
  useEffect(() => {
    setTimeout(() => {
      inputRefs.current[0]?.focus();
    }, 100);

    // Cleanup function to clear the timer when component unmounts
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, []);

  const handleOtpChange = (text: string, index: number) => {
    // Chỉ cho phép nhập số
    if (!/^[0-9]*$/.test(text)) return;

    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);
    setError("");

    // Tự động focus vào ô tiếp theo nếu đã nhập
    if (text.length === 1 && index < 5) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    // Xử lý khi nhấn phím xóa
    if (e.nativeEvent.key === "Backspace" && index > 0 && otp[index] === "") {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const getOtpString = () => otp.join("");

  const handleVerifyOTP = async () => {
    const otpString = getOtpString();

    if (otpString.length !== 6) {
      setError("Vui lòng nhập đầy đủ mã OTP 6 số");
      return;
    }

    setIsLoading(true);

    try {
      
      const result = await verifyOTP(phone, otpString, OtpPurpose.PASSWORD_RESET);

      if (result) {
        // Chuyển hướng đến màn hình đặt mật khẩu mới
        router.push({
          pathname: "/new-password",
          params: { phone },
        });
      } else {
        setError("Mã OTP không chính xác. Vui lòng thử lại.");
      }
    } catch (error) {
      console.error("OTP verification error:", error);
      setError("Đã xảy ra lỗi. Vui lòng thử lại sau.");
    } finally {
      setIsLoading(false);
    }
  };

  // Function to start the countdown timer
  const startCountdown = () => {
    // Set initial countdown to 60 seconds
    setCountdown(60);

    // Clear any existing timer
    if (timerRef.current) {
      clearInterval(timerRef.current);
    }

    // Create a new timer that decrements the countdown every second
    timerRef.current = setInterval(() => {
      setCountdown((prevCount) => {
        if (prevCount <= 1) {
          // When countdown reaches 0, clear the interval
          if (timerRef.current) {
            clearInterval(timerRef.current);
          }
          return 0;
        }
        return prevCount - 1;
      });
    }, 1000);
  };

  const handleResendOTP = async () => {
    // If countdown is still active, don't allow resending
    if (countdown > 0) return;

    // Generate a new mock OTP (in a real app, this would be handled by the API)
    const mockOTP = "123456";
    await AsyncStorage.setItem("@resetPhone", phone);
    await AsyncStorage.setItem("@otp", mockOTP);

    // Start the countdown timer
    startCountdown();
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === "ios" ? "padding" : undefined}
      keyboardVerticalOffset={Platform.OS === "ios" ? 64 : 0}
    >
      <StatusBar style="dark" />
      <Stack.Screen
        options={{
          headerShown: false,
        }}
      />

      {/* Absolute positioned back button */}
      <TouchableOpacity
        style={styles.backButton}
        onPress={() => router.back()}
        activeOpacity={0.7}
      >
        <MaterialIcons name="arrow-back" size={24} color="#333" />
      </TouchableOpacity>

      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logoContainer}>
          <Image
            source={require("@/assets/images/logo.png")}
            style={styles.logo}
            resizeMode="contain"
          />
        </View>

        <View style={styles.formContainer}>
          <ThemedText style={styles.subtitle}>
            Nhập mã OTP 6 số đã được gửi đến số điện thoại {phone}
          </ThemedText>

          <View style={styles.otpContainer}>
            {otp.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref) => (inputRefs.current[index] = ref)}
                style={styles.otpInput}
                value={digit}
                onChangeText={(text) => handleOtpChange(text, index)}
                onKeyPress={(e) => handleKeyPress(e, index)}
                keyboardType="number-pad"
                maxLength={1}
                selectTextOnFocus
              />
            ))}
          </View>

          {error ? (
            <View style={styles.errorContainer}>
              <ThemedText style={styles.errorText}>{error}</ThemedText>
            </View>
          ) : null}

          <Button
            title="Xác nhận"
            onPress={handleVerifyOTP}
            loading={isLoading}
            style={styles.submitButton}
          />

          <TouchableOpacity
            style={styles.resendButton}
            onPress={handleResendOTP}
            disabled={isLoading || countdown > 0}
          >
            <ThemedText style={styles.resendLabel}>
              Không nhận được mã?
            </ThemedText>
            <ThemedText
              style={[styles.resendText, countdown > 0 && styles.disabledText]}
            >
              {countdown > 0 ? `Gửi lại mã (${countdown}s)` : "Gửi lại mã"}
            </ThemedText>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#fff",
  },
  backButton: {
    position: "absolute",
    top: Platform.OS === "ios" ? 50 : 30,
    left: 16,
    zIndex: 10,
    backgroundColor: "rgba(255, 255, 255, 0.8)",
    width: 48,
    height: 48,
    justifyContent: "center",
    alignItems: "center",
    elevation: 3,
  },
  scrollContent: {
    flexGrow: 1,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 52,
    marginBottom: 20,
  },
  logo: {
    width: 200,
    height: 120,
  },
  formContainer: {
    flex: 1,
    paddingHorizontal: 24,
  },
  subtitle: {
    textAlign: "center",
    color: "#687076",
    marginBottom: 32,
    lineHeight: 22,
  },
  otpContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginBottom: 32,
  },
  otpInput: {
    width: 45,
    height: 55,
    borderWidth: 1,
    borderColor: "#E6E8EB",
    borderRadius: 8,
    textAlign: "center",
    fontSize: 24,
    fontWeight: "bold",
  },
  errorContainer: {
    backgroundColor: "#FFEBEE",
    borderRadius: 8,
    padding: 12,
    marginBottom: 16,
    borderLeftWidth: 4,
    borderLeftColor: "#E53935",
  },
  errorText: {
    color: "#E53935",
    fontSize: 14,
  },
  submitButton: {
    marginBottom: 16,
  },
  resendButton: {
    alignItems: "center",
    padding: 8,
  },
  resendText: {
    color: Colors.primary,
    fontWeight: "700",
  },
  resendLabel: {
    color: "#687076",
  },
  disabledText: {
    color: "#9E9E9E",
  },
});
