import React from 'react';
import { Text as RNText, TextProps, StyleSheet } from 'react-native';
import { FONTS, TEXT_STYLES } from '@/constants/Typography';

/**
 * A global text component that applies the app's font family to all text
 */
export function GlobalText(props: TextProps): React.ReactElement {
  const { style, ...otherProps } = props;
  
  return (
    <RNText
      style={[TEXT_STYLES.default, style]}
      {...otherProps}
    />
  );
}

// Default style for all text
const styles = StyleSheet.create({
  text: {
    fontFamily: FONTS.REGULAR,
  },
});
