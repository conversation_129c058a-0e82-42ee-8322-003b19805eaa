import React, { RefObject } from 'react';
import { View, Text, TouchableOpacity, TextInput, StyleSheet } from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

interface LocationInputsProps {
  pickupAddress: string;
  destinationAddress: string;
  setActiveInput: (input: 'pickup' | 'destination' | null) => void;
  setShowSuggestions: (show: boolean) => void;
  setSearchQuery: (query: string) => void;
  searchInputRef: RefObject<TextInput>;
}

const LocationInputs: React.FC<LocationInputsProps> = ({
  pickupAddress,
  destinationAddress,
  setActiveInput,
  setShowSuggestions,
  setSearchQuery,
  searchInputRef,
}) => {
  const insets = useSafeAreaInsets();

  return (
    <View style={[styles.searchContainer, { top: insets.top + 10 }]}>
      <TouchableOpacity
        style={[
          styles.locationInputContainer,
          { borderBottomWidth: 1, borderBottomColor: "#f0f0f0" },
        ]}
        onPress={() => {
          setActiveInput("pickup");
          setShowSuggestions(true);
          setSearchQuery(pickupAddress);
          setTimeout(() => {
            searchInputRef.current?.focus();
          }, 100);
        }}
      >
        <View style={styles.locationIconContainer}>
          <Ionicons name="location" size={20} color="#ffde59" />
        </View>
        <View style={styles.locationTextContainer}>
          <Text style={styles.locationLabel}>Điểm đón</Text>
          {pickupAddress ? (
            <Text style={styles.locationText} numberOfLines={2}>
              {pickupAddress}
            </Text>
          ) : (
            <Text style={styles.locationPlaceholder}>Chọn điểm đón</Text>
          )}
        </View>
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.locationInputContainer}
        onPress={() => {
          setActiveInput("destination");
          setShowSuggestions(true);
          setSearchQuery(destinationAddress);
          setTimeout(() => {
            searchInputRef.current?.focus();
          }, 100);
        }}
      >
        <View style={styles.locationIconContainer}>
          <Ionicons name="flag" size={20} color="#2E64FE" />
        </View>
        <View style={styles.locationTextContainer}>
          <Text style={styles.locationLabel}>Điểm đến</Text>
          {destinationAddress ? (
            <Text style={styles.locationText} numberOfLines={2}>
              {destinationAddress}
            </Text>
          ) : (
            <Text style={styles.locationPlaceholder}>Chọn điểm đến</Text>
          )}
        </View>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  searchContainer: {
    position: 'absolute',
    left: 16,
    right: 16,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 4,
    zIndex: 10,
  },
  locationInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  locationIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: '#f5f5f5',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  locationTextContainer: {
    flex: 1,
  },
  locationLabel: {
    fontSize: 12,
    color: '#666',
    marginBottom: 2,
  },
  locationText: {
    fontSize: 14,
    color: '#333',
  },
  locationPlaceholder: {
    fontSize: 14,
    color: '#999',
  },
});

export default LocationInputs;
