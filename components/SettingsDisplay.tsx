import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator, ScrollView, TouchableOpacity } from 'react-native';
import useSettings from '@/hooks/useSettings';

/**
 * Component to display application settings
 * 
 * This component fetches and displays all application settings.
 */
const SettingsDisplay = () => {
  // Use our custom hook to access settings
  const { 
    settingsMap, 
    loading, 
    error, 
    fetchSettings,
    getSetting,
    getSuburbanDayFee
  } = useSettings({ 
    fetchOnMount: true, 
    useMap: true 
  });

  // Handle refresh
  const handleRefresh = () => {
    fetchSettings();
  };

  // Get suburban day fee
  const suburbanDayFee = getSuburbanDayFee();

  return (
    <View style={styles.container}>
      <Text style={styles.title}>Cài đặt ứng dụng</Text>
      
      {/* Loading indicator */}
      {loading && (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color="#2E64FE" />
          <Text style={styles.loadingText}>Đang tải cài đặt...</Text>
        </View>
      )}
      
      {/* Error message */}
      {error && (
        <View style={styles.errorContainer}>
          <Text style={styles.errorText}>Lỗi: {error}</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
            <Text style={styles.retryButtonText}>Thử lại</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {/* Settings display */}
      {!loading && !error && Object.keys(settingsMap).length > 0 && (
        <ScrollView style={styles.settingsContainer}>
          {/* Suburban Day Fee Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Phí thuê tài xế ngoại thành</Text>
            {suburbanDayFee ? (
              <View style={styles.settingItem}>
                <Text style={styles.settingValue}>
                  {JSON.stringify(suburbanDayFee, null, 2)}
                </Text>
              </View>
            ) : (
              <Text style={styles.noDataText}>Không có dữ liệu</Text>
            )}
          </View>
          
          {/* All Settings Section */}
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Tất cả cài đặt</Text>
            {Object.entries(settingsMap).map(([key, value]) => (
              <View key={key} style={styles.settingItem}>
                <Text style={styles.settingKey}>{key}</Text>
                <Text style={styles.settingValue}>
                  {typeof value === 'object' 
                    ? JSON.stringify(value, null, 2) 
                    : String(value)}
                </Text>
              </View>
            ))}
          </View>
          
          {/* Refresh button */}
          <TouchableOpacity style={styles.refreshButton} onPress={handleRefresh}>
            <Text style={styles.refreshButtonText}>Làm mới</Text>
          </TouchableOpacity>
        </ScrollView>
      )}
      
      {/* No settings message */}
      {!loading && !error && Object.keys(settingsMap).length === 0 && (
        <View style={styles.noDataContainer}>
          <Text style={styles.noDataText}>Không có cài đặt nào</Text>
          <TouchableOpacity style={styles.retryButton} onPress={handleRefresh}>
            <Text style={styles.retryButtonText}>Làm mới</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 16,
    backgroundColor: '#fff',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 16,
    color: '#333',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 16,
    color: '#666',
  },
  errorContainer: {
    padding: 16,
    backgroundColor: '#ffeeee',
    borderRadius: 8,
    marginBottom: 16,
    alignItems: 'center',
  },
  errorText: {
    color: '#d32f2f',
    marginBottom: 12,
  },
  settingsContainer: {
    flex: 1,
  },
  section: {
    marginBottom: 24,
    backgroundColor: '#f9f9f9',
    borderRadius: 8,
    padding: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 12,
    color: '#333',
  },
  settingItem: {
    marginBottom: 16,
    padding: 12,
    backgroundColor: '#fff',
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  settingKey: {
    fontSize: 16,
    fontWeight: '500',
    color: '#2E64FE',
    marginBottom: 8,
  },
  settingValue: {
    fontSize: 14,
    color: '#666',
  },
  noDataContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noDataText: {
    fontSize: 16,
    color: '#666',
    marginBottom: 16,
  },
  retryButton: {
    backgroundColor: '#2E64FE',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 4,
  },
  retryButtonText: {
    color: '#fff',
    fontWeight: '500',
  },
  refreshButton: {
    backgroundColor: '#2E64FE',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
    marginTop: 16,
    marginBottom: 32,
  },
  refreshButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default SettingsDisplay;
