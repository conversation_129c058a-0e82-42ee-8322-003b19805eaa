import React from 'react';
import { Text as RNText, TextProps, TextStyle } from 'react-native';
import { FONTS, FONT_WEIGHTS } from '@/constants/Typography';

/**
 * This component overrides the default Text component in React Native
 * to apply the app's font family to all text
 */
const TextWithDefaultProps = (props: TextProps) => {
  const { style, ...otherProps } = props;

  // Get the font weight from style if it exists
  const flattenedStyle = style ? (Array.isArray(style) ? Object.assign({}, ...style) : style) : {};
  const fontWeight = (flattenedStyle as TextStyle).fontWeight || 'normal';
  const fontStyle = (flattenedStyle as TextStyle).fontStyle;

  // Determine the font family based on weight and style
  let fontFamily = FONT_WEIGHTS[fontWeight];
  if (fontStyle === 'italic') {
    fontFamily = FONTS.ITALIC;
  }

  return (
    <RNText
      style={[{ fontFamily }, style]}
      {...otherProps}
    />
  );
};

// Initialize the default props directly in the component
const Text = TextWithDefaultProps;

// Export the Text component
export { Text };

// Initialize the default props from RNText
// This is done here to avoid TypeScript errors in _layout.tsx
if (RNText.defaultProps) {
  // Apply any default props from RNText
  // This is done at module level, not in the component
};

