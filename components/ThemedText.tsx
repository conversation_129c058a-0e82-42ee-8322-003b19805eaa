import { Text, type TextProps } from "react-native";

import { TEXT_STYLES } from "@/constants/Typography";

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: "default" | "title" | "defaultSemiBold" | "subtitle" | "link";
};

export function ThemedText({
  style,
  lightColor,
  darkColor,
  type = "default",
  ...rest
}: ThemedTextProps) {
  const color = lightColor;

  return (
    <Text
      style={[
        { color },
        type === "default" ? TEXT_STYLES.default : undefined,
        type === "title" ? TEXT_STYLES.title : undefined,
        type === "defaultSemiBold" ? TEXT_STYLES.defaultSemiBold : undefined,
        type === "subtitle" ? TEXT_STYLES.subtitle : undefined,
        type === "link" ? TEXT_STYLES.link : undefined,
        style,
      ]}
      {...rest}
    />
  );
}

// Styles are now imported from Typography.ts
