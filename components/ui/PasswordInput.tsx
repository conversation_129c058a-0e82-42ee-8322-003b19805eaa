import React, { useState, useRef, useEffect } from 'react';
import {
  View,
  TextInput,
  StyleSheet,
  Text,
  Keyboard,
  ViewStyle,
  Pressable,
} from 'react-native';
import { Colors } from '@/constants/Colors';
import { FONTS } from '@/constants/Typography';

interface PasswordInputProps {
  label?: string;
  error?: string;
  containerStyle?: ViewStyle;
  value: string;
  onChangeText: (text: string) => void;
  maxLength?: number;
}

export function PasswordInput({
  label,
  error,
  containerStyle,
  value,
  onChangeText,
  maxLength = 6,
}: PasswordInputProps) {
  const inputRef = useRef<TextInput>(null);
  const [isFocused, setIsFocused] = useState(false);

  // Create an array of the current password characters
  const passwordChars = value.split('');
  
  // Fill the array with empty strings if needed to match maxLength
  while (passwordChars.length < maxLength) {
    passwordChars.push('');
  }

  // Focus the input when the component mounts
  useEffect(() => {
    const timer = setTimeout(() => {
      inputRef.current?.focus();
    }, 100);
    return () => clearTimeout(timer);
  }, []);

  const handlePress = () => {
    inputRef.current?.focus();
  };

  return (
    <View style={[styles.container, containerStyle]}>
      {label && <Text style={styles.label}>{label}</Text>}

      <Pressable onPress={handlePress}>
        <View style={styles.boxesContainer}>
          {/* Hidden text input to capture keyboard input */}
          <TextInput
            ref={inputRef}
            value={value}
            onChangeText={(text) => {
              // Only allow numbers and limit to maxLength
              if (/^\d*$/.test(text) && text.length <= maxLength) {
                onChangeText(text);
              }
            }}
            style={styles.hiddenInput}
            keyboardType="number-pad"
            maxLength={maxLength}
            onFocus={() => setIsFocused(true)}
            onBlur={() => setIsFocused(false)}
            secureTextEntry={false}
          />

          {/* Display boxes for each character */}
          {passwordChars.map((char, index) => (
            <View
              key={index}
              style={[
                styles.box,
                isFocused && index === value.length && styles.focusedBox,
                error ? styles.errorBox : null,
              ]}
            >
              {char ? (
                <View style={styles.dot} />
              ) : null}
            </View>
          ))}
        </View>
      </Pressable>

      {error && <Text style={styles.errorText}>{error}</Text>}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 16,
  },
  label: {
    fontSize: 14,
    marginBottom: 6,
    color: '#687076',
    fontFamily: FONTS.REGULAR,
  },
  boxesContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 8,
  },
  box: {
    width: 48,
    height: 48,
    borderWidth: 1,
    borderColor: '#E6E8EB',
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff',
  },
  focusedBox: {
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  errorBox: {
    borderColor: '#E53935',
  },
  dot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: '#11181C',
  },
  errorText: {
    color: '#E53935',
    fontSize: 12,
    marginTop: 4,
    fontFamily: FONTS.REGULAR,
  },
  hiddenInput: {
    position: 'absolute',
    opacity: 0,
    height: 0,
    width: 0,
  },
});
