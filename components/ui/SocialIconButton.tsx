import React from 'react';
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  TouchableOpacity,
  TouchableOpacityProps,
  ViewStyle
} from 'react-native';

interface SocialIconButtonProps extends TouchableOpacityProps {
  provider: 'facebook' | 'google' | 'apple';
  style?: ViewStyle;
}

export function SocialIconButton({
  provider,
  style,
  ...rest
}: SocialIconButtonProps) {
  const getProviderInfo = () => {
    switch (provider) {
      case 'facebook':
        return {
          icon: require('@/assets/images/facebook-icon.png'),
          backgroundColor: '#1877F2',
        };
      case 'google':
        return {
          icon: require('@/assets/images/google-icon.png'),
          backgroundColor: '#FFFFFF',
        };
      case 'apple':
        return {
          icon: require('@/assets/images/apple-icon.png'),
          backgroundColor: '#fff',
        };
    }
  };

  const providerInfo = getProviderInfo();

  const buttonStyle = [
    styles.button,
    { backgroundColor: providerInfo.backgroundColor },
    provider === 'google' && styles.googleButton,
    style
  ];

  return (
    <TouchableOpacity
      style={buttonStyle}
      activeOpacity={0.8}
      {...rest}
    >
      <Image
        source={providerInfo.icon as ImageSourcePropType}
        style={styles.icon}
        resizeMode="contain"
      />
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    width: 50,
    height: 50,
    borderRadius: 25,
    justifyContent: 'center',
    alignItems: 'center',
    marginHorizontal: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  googleButton: {
    borderWidth: 1,
    borderColor: '#DADCE0',
  },
  icon: {
    width: 24,
    height: 24,
  },
});
