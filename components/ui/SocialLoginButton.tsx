import React from 'react';
import {
  Image,
  ImageSourcePropType,
  StyleSheet,
  Text,
  TextStyle,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle
} from 'react-native';

interface SocialLoginButtonProps extends TouchableOpacityProps {
  provider: 'facebook' | 'google' | 'apple';
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export function SocialLoginButton({
  provider,
  style,
  textStyle,
  ...rest
}: SocialLoginButtonProps) {
  const getProviderInfo = () => {
    switch (provider) {
      case 'facebook':
        return {
          icon: require('@/assets/images/facebook-icon.png'),
          text: 'Tiếp tục với Facebook',
          color: '#1877F2',
        };
      case 'google':
        return {
          icon: require('@/assets/images/google-icon.png'),
          text: 'Tiếp tục với Google',
          color: '#FFFFFF',
        };
      case 'apple':
        return {
          icon: require('@/assets/images/apple-icon.png'),
          text: 'Tiếp tục với Apple',
          color: '#000000',
        };
    }
  };

  const providerInfo = getProviderInfo();
  
  const buttonStyle = [
    styles.button,
    { backgroundColor: providerInfo.color },
    provider === 'google' && styles.googleButton,
    style
  ];

  const textColor = provider === 'google' ? '#000' : '#fff';
  
  return (
    <TouchableOpacity
      style={buttonStyle}
      activeOpacity={0.8}
      {...rest}
    >
      <View style={styles.buttonContent}>
        <Image 
          source={providerInfo.icon as ImageSourcePropType}
          style={styles.icon}
          resizeMode="contain"
        />
        <Text style={[styles.text, { color: textColor }, textStyle]}>
          {providerInfo.text}
        </Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  button: {
    height: 50,
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
    marginVertical: 8,
  },
  googleButton: {
    borderWidth: 1,
    borderColor: '#DADCE0',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    width: 24,
    height: 24,
    marginRight: 12,
  },
  text: {
    fontSize: 16,
    fontWeight: '600',
  },
});
