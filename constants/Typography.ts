/**
 * Global typography styles for the application
 */

import { StyleSheet, TextStyle } from 'react-native';

// Font family names
export const FONTS = {
  REGULAR: 'Nunito',
  ITALIC: 'NunitoItalic',
};

// Font weights mapped to font families
export const FONT_WEIGHTS: Record<TextStyle['fontWeight'], string> = {
  'normal': FONTS.REGULAR,
  'bold': FONTS.REGULAR,
  '100': FONTS.REGULAR,
  '200': FONTS.REGULAR,
  '300': FONTS.REGULAR,
  '400': FONTS.REGULAR,
  '500': FONTS.REGULAR,
  '600': FONTS.REGULAR,
  '700': FONTS.REGULAR,
  '800': FONTS.REGULAR,
  '900': FONTS.REGULAR,
};

// Global text styles
export const TEXT_STYLES = StyleSheet.create({
  default: {
    fontFamily: FONTS.REGULAR,
    fontSize: 16,
    lineHeight: 24,
  },
  defaultSemiBold: {
    fontFamily: FONTS.REGULAR,
    fontSize: 16,
    lineHeight: 24,
    fontWeight: '600',
  },
  title: {
    fontFamily: FONTS.REGULAR,
    fontSize: 32,
    fontWeight: 'bold',
    lineHeight: 38,
  },
  subtitle: {
    fontFamily: FONTS.REGULAR,
    fontSize: 20,
    fontWeight: 'bold',
    lineHeight: 26,
  },
  link: {
    fontFamily: FONTS.REGULAR,
    lineHeight: 30,
    fontSize: 16,
  },
  small: {
    fontFamily: FONTS.REGULAR,
    fontSize: 14,
    lineHeight: 20,
  },
  italic: {
    fontFamily: FONTS.ITALIC,
    fontStyle: 'italic',
  },
});
