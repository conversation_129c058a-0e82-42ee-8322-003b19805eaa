import React, { createContext, useState, useContext, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import {
  register,
  signIn as apiSignIn,
  resendOTP as apiResendOTP,
  verifyOTP as apiVerifyOTP,
} from "@/api/auth.api";

type User = {
  id: string | number;
  name: string;
  phone: string;
};

export enum OtpPurpose {
  REGISTRATION = 'REGISTRATION',
  PASSWORD_RESET = 'PASSWORD_RESET',
  FORGOT_PASSWORD = 'FORGOT_PASSWORD',
  VERIFY_PHONE = 'VERIFY_PHONE',
}

type AuthContextType = {
  user: User | null;
  isLoading: boolean;
  signIn: (phone: string, password: string) => Promise<boolean>;
  signUp: (name: string, phone: string, password: string) => Promise<boolean>;
  signOut: () => Promise<void>;
  resetPassword: (phone: string) => Promise<boolean>;
  setNewPassword: (phone: string, password: string) => Promise<boolean>;
  updateUser: (updatedUser: User) => Promise<boolean>;
  resendOTP: (phone: string, type: OtpPurpose) => Promise<boolean>;
  verifyOTP: (phone: string, otp: string, type: OtpPurpose) => Promise<boolean>;
};

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check if user is already logged in
    const loadUser = async () => {
      try {
        const userJson = await AsyncStorage.getItem("@user");
        if (userJson) {
          setUser(JSON.parse(userJson));
        }
      } catch (e) {
        console.error("Failed to load user from storage", e);
      } finally {
        setIsLoading(false);
      }
    };

    loadUser();
  }, []);

  const signIn = async (phone: string, password: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await apiSignIn(phone, password);
      if (response && response.user) {
        const userData = {
          id: response.user.id,
          name: response.user.name,
          phone: response.user.phone,
          photo: response.user.photo,
          apiToken: response.apiToken,
          account_locked_reason: response.user.account_locked_reason,
          lat: response.user.lat,
          lng: response.user.lng,
          is_active: response.user.is_active,
          is_phone_confirmed: response.user.is_phone_confirmed,
          referral_code: response.user.referral_code,
          updated_at: response.user.updated_at,
        };
        setUser(userData);
        await AsyncStorage.setItem("@user", JSON.stringify(userData));
        await AsyncStorage.setItem("@apiToken", response.apiToken);
        return true;
      }
      return false;
    } catch (e) {
      console.error("Sign in failed", e);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const signUp = async (
    name: string,
    phone: string,
    password: string
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const user = await register({
        name,
        phone,
        password,
      });

      if (user) {
        const userData = {
          id: user.id,
          name: user.name,
          phone: user.phone,
          account_locked_reason: user.account_locked_reason,
          fcm_token: user.fcm_token,
          lat: user.lat,
          lng: user.lng,
          is_active: user.is_active,
          is_phone_confirmed: user.is_phone_confirmed,
          photo: user.photo,
          referral_code: user.referral_code,
          updated_at: user.updated_at,
        };
        setUser(userData);
        await AsyncStorage.setItem("@user", JSON.stringify(userData));
        return true;
      }
      return false;
    } catch (e) {
      throw e;
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async (): Promise<void> => {
    setIsLoading(true);
    try {
      await AsyncStorage.removeItem("@user");
      setUser(null);
    } catch (e) {
      console.error("Sign out failed", e);
    } finally {
      setIsLoading(false);
    }
  };

  // Mock function to reset password (send OTP)
  const resetPassword = async (phone: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1500));

      // For demo purposes, always succeed
      // In a real app, this would send an OTP to the user's phone
      await AsyncStorage.setItem("@resetPhone", phone);
      await AsyncStorage.setItem("@otp", "123456"); // Demo OTP

      return true;
    } catch (e) {
      console.error("Reset password failed", e);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  const verifyOTP = async (
    phone: string,
    otp: string,
    type: OtpPurpose
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await apiVerifyOTP(phone, otp, type);

      if (response && response.success) {
        return true;
      }
      return false;
    } catch (e) {
      console.error("OTP verification failed", e);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to set new password
  const setNewPassword = async (
    phone: string,
    password: string
  ): Promise<boolean> => {
    setIsLoading(true);
    try {
      // This is a placeholder for the actual API call
      // In a real implementation, you would call an API endpoint to reset the password
      console.log(`Resetting password for ${phone} to ${password}`);

      // For now, we'll simulate success and clear the reset data
      await AsyncStorage.removeItem("@resetPhone");
      await AsyncStorage.removeItem("@otp");

      return true;
    } catch (e) {
      console.error("Set new password failed", e);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to update user profile
  const updateUser = async (updatedUser: User): Promise<boolean> => {
    setIsLoading(true);
    try {
      // Simulate API call delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Update user in state and storage
      setUser(updatedUser);
      await AsyncStorage.setItem("@user", JSON.stringify(updatedUser));

      return true;
    } catch (e) {
      console.error("Update user failed", e);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Function to resend OTP
  const resendOTP = async (phone: string, type: OtpPurpose): Promise<boolean> => {
    setIsLoading(true);
    try {
      await apiResendOTP(phone, type);

      return true;
    } catch (e) {
      console.error("Resend OTP failed", e);
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <AuthContext.Provider
      value={{
        user,
        isLoading,
        signIn,
        signUp,
        signOut,
        resetPassword,
        setNewPassword,
        updateUser,
        resendOTP,
        verifyOTP,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
