import { useState, useEffect } from 'react';

/**
 * Hook để debounce giá trị, gi<PERSON>p giảm số lần gọi API khi người dùng đang nhập
 * @param value Giá trị cần debounce
 * @param delay Thời gian delay tính bằng milliseconds
 * @returns Giá trị sau khi đã debounce
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    // Tạo timeout để cập nhật giá trị debounced sau delay
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    // Hủy timeout nếu giá trị thay đổi hoặc component unmount
    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}
