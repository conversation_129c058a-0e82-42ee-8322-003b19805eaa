import { useEffect, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { fetchAllSettings } from "@/store/slices/settingsSlice";

/**
 * Custom hook to access and fetch settings
 *
 * @param options Configuration options
 * @param options.fetchOnMount Whether to fetch settings when the component mounts
 * @param options.forceRefresh Whether to force a refresh of settings
 * @returns Settings state and utility functions
 */
export const useSettings = ({
  fetchOnMount = true,
  forceRefresh = false,
} = {}) => {
  const dispatch = useAppDispatch();
  const { settings, loading, error } = useAppSelector(
    (state) => state.settings
  );


  // Fetch settings if needed
  const fetchSettings = useCallback(() => {
    dispatch(fetchAllSettings());
  }, [dispatch]);

  // Fetch settings on mount if needed
  useEffect(() => {
    if (fetchOnMount || forceRefresh) {
      fetchSettings();
    }
  }, [fetchOnMount, forceRefresh, fetchSettings]);

  // Get a specific setting value
  const getSetting = (key: string, defaultValue: any = null) => {
    // First try to find the setting using setting_key (old format)
    const settingOldFormat = settings.find((s) => s.setting_key === key);
    if (settingOldFormat) {
      return settingOldFormat.setting_value;
    }

    // Then try to find the setting using key (new format)
    const settingNewFormat = settings.find((s) => s.key === key);
    if (settingNewFormat) {
      // Handle nested value structure
      if (settingNewFormat.value && typeof settingNewFormat.value === 'object' && 'value' in settingNewFormat.value) {
        return settingNewFormat.value.value;
      }
      return settingNewFormat.value;
    }

    return defaultValue;
  };

  // Get suburban day fee
  const getSuburbanDayFee = () => {
    return getSetting("saygo_suburban_day_fee");
  };

  return {
    settings,
    loading,
    error,
    fetchSettings,
    getSetting,
    getSuburbanDayFee,
  };
};

export default useSettings;
