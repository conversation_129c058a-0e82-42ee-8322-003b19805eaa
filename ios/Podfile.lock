PODS:
  - boost (1.84.0)
  - DoubleConversion (1.1.6)
  - EXConstants (17.0.8):
    - ExpoModulesCore
  - EXImageLoader (4.7.0):
    - ExpoModulesCore
    - React-Core
  - EXJSONUtils (0.14.0)
  - EXManifests (0.15.8):
    - ExpoModulesCore
  - Expo (52.0.46):
    - ExpoModulesCore
  - expo-dev-client (5.0.20):
    - EXManifests
    - expo-dev-launcher
    - expo-dev-menu
    - expo-dev-menu-interface
    - EXUpdatesInterface
  - expo-dev-launcher (5.0.35):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Main (= 5.0.35)
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Main (5.0.35):
    - DoubleConversion
    - EXManifests
    - expo-dev-launcher/Unsafe
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-launcher/Unsafe (5.0.35):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu
    - expo-dev-menu-interface
    - ExpoModulesCore
    - EXUpdatesInterface
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu (6.0.25):
    - DoubleConversion
    - expo-dev-menu/Main (= 6.0.25)
    - expo-dev-menu/ReactNativeCompatibles (= 6.0.25)
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu-interface (1.9.3)
  - expo-dev-menu/Main (6.0.25):
    - DoubleConversion
    - EXManifests
    - expo-dev-menu-interface
    - expo-dev-menu/Vendored
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/ReactNativeCompatibles (6.0.25):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/SafeAreaView (6.0.25):
    - DoubleConversion
    - ExpoModulesCore
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - expo-dev-menu/Vendored (6.0.25):
    - DoubleConversion
    - expo-dev-menu/SafeAreaView
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoAsset (11.0.5):
    - ExpoModulesCore
  - ExpoBlur (14.0.3):
    - ExpoModulesCore
  - ExpoClipboard (7.0.1):
    - ExpoModulesCore
  - ExpoFileSystem (18.0.12):
    - ExpoModulesCore
  - ExpoFont (13.0.4):
    - ExpoModulesCore
  - ExpoHaptics (14.0.1):
    - ExpoModulesCore
  - ExpoHead (4.0.20):
    - ExpoModulesCore
  - ExpoImagePicker (15.0.7):
    - ExpoModulesCore
  - ExpoKeepAwake (14.0.3):
    - ExpoModulesCore
  - ExpoLinearGradient (14.0.2):
    - ExpoModulesCore
  - ExpoLinking (7.0.5):
    - ExpoModulesCore
  - ExpoLocation (18.0.10):
    - ExpoModulesCore
  - ExpoModulesCore (2.2.3):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTAppDelegate
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - ExpoSplashScreen (0.29.24):
    - ExpoModulesCore
  - ExpoSymbols (0.2.2):
    - ExpoModulesCore
  - ExpoSystemUI (4.0.9):
    - ExpoModulesCore
  - ExpoWebBrowser (14.0.2):
    - ExpoModulesCore
  - EXUpdatesInterface (1.0.0):
    - ExpoModulesCore
  - fast_float (6.1.4)
  - FBLazyVector (0.76.9)
  - fmt (11.0.2)
  - glog (0.3.5)
  - hermes-engine (0.76.9):
    - hermes-engine/Pre-built (= 0.76.9)
  - hermes-engine/Pre-built (0.76.9)
  - maplibre-react-native (10.0.0-alpha.5):
    - maplibre-react-native/DynamicLibrary (= 10.0.0-alpha.5)
    - React
    - React-Core
  - maplibre-react-native/DynamicLibrary (10.0.0-alpha.5):
    - React
    - React-Core
  - RCT-Folly (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Default (= 2024.10.14.00)
  - RCT-Folly/Default (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCT-Folly/Fabric (2024.10.14.00):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
  - RCTDeprecation (0.76.9)
  - RCTRequired (0.76.9)
  - RCTTypeSafety (0.76.9):
    - FBLazyVector (= 0.76.9)
    - RCTRequired (= 0.76.9)
    - React-Core (= 0.76.9)
  - React (0.76.9):
    - React-Core (= 0.76.9)
    - React-Core/DevSupport (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-RCTActionSheet (= 0.76.9)
    - React-RCTAnimation (= 0.76.9)
    - React-RCTBlob (= 0.76.9)
    - React-RCTImage (= 0.76.9)
    - React-RCTLinking (= 0.76.9)
    - React-RCTNetwork (= 0.76.9)
    - React-RCTSettings (= 0.76.9)
    - React-RCTText (= 0.76.9)
    - React-RCTVibration (= 0.76.9)
  - React-callinvoker (0.76.9)
  - React-Core (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/CoreModulesHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/Default (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/DevSupport (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-Core/RCTWebSocket (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTActionSheetHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTAnimationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTBlobHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTImageHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTLinkingHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTNetworkHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTSettingsHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTTextHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTVibrationHeaders (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-Core/RCTWebSocket (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTDeprecation
    - React-Core/Default (= 0.76.9)
    - React-cxxreact
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimescheduler
    - React-utils
    - SocketRocket (= 0.7.1)
    - Yoga
  - React-CoreModules (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - RCTTypeSafety
    - React-Core/CoreModulesHeaders
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTBlob
    - React-RCTImage
    - ReactCodegen
    - ReactCommon
    - SocketRocket
  - React-cxxreact (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-debug
    - React-jsi
    - React-jsinspector
    - React-logger
    - React-perflogger
    - React-runtimeexecutor
    - React-timing
  - React-debug (0.76.9)
  - React-defaultsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-domnativemodule
    - React-Fabric
    - React-featureflags
    - React-featureflagsnativemodule
    - React-graphics
    - React-idlecallbacksnativemodule
    - React-ImageManager
    - React-microtasksnativemodule
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-domnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/animations (= 0.76.9)
    - React-Fabric/attributedstring (= 0.76.9)
    - React-Fabric/componentregistry (= 0.76.9)
    - React-Fabric/componentregistrynative (= 0.76.9)
    - React-Fabric/components (= 0.76.9)
    - React-Fabric/core (= 0.76.9)
    - React-Fabric/dom (= 0.76.9)
    - React-Fabric/imagemanager (= 0.76.9)
    - React-Fabric/leakchecker (= 0.76.9)
    - React-Fabric/mounting (= 0.76.9)
    - React-Fabric/observers (= 0.76.9)
    - React-Fabric/scheduler (= 0.76.9)
    - React-Fabric/telemetry (= 0.76.9)
    - React-Fabric/templateprocessor (= 0.76.9)
    - React-Fabric/uimanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/animations (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/attributedstring (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/componentregistrynative (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/components/legacyviewmanagerinterop (= 0.76.9)
    - React-Fabric/components/root (= 0.76.9)
    - React-Fabric/components/view (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/legacyviewmanagerinterop (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/root (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/components/view (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
    - Yoga
  - React-Fabric/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/dom (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/imagemanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/leakchecker (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/mounting (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/observers/events (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/scheduler (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/observers/events
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-performancetimeline
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/telemetry (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/templateprocessor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric/uimanager/consistency (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-Fabric/uimanager/consistency (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCommon/turbomodule/core
  - React-FabricComponents (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components (= 0.76.9)
    - React-FabricComponents/textlayoutmanager (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-FabricComponents/components/inputaccessory (= 0.76.9)
    - React-FabricComponents/components/iostextinput (= 0.76.9)
    - React-FabricComponents/components/modal (= 0.76.9)
    - React-FabricComponents/components/rncore (= 0.76.9)
    - React-FabricComponents/components/safeareaview (= 0.76.9)
    - React-FabricComponents/components/scrollview (= 0.76.9)
    - React-FabricComponents/components/text (= 0.76.9)
    - React-FabricComponents/components/textinput (= 0.76.9)
    - React-FabricComponents/components/unimplementedview (= 0.76.9)
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/inputaccessory (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/iostextinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/modal (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/rncore (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/safeareaview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/scrollview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/text (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/textinput (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/components/unimplementedview (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricComponents/textlayoutmanager (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-cxxreact
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/core
    - Yoga
  - React-FabricImage (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly/Fabric
    - RCTRequired
    - RCTTypeSafety
    - React-Fabric
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsiexecutor
    - React-logger
    - React-rendererdebug
    - React-utils
    - ReactCommon
    - Yoga
  - React-featureflags (0.76.9)
  - React-featureflagsnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-graphics (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - RCT-Folly/Fabric
    - React-jsi
    - React-jsiexecutor
    - React-utils
  - React-hermes (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-perflogger
    - React-runtimeexecutor
  - React-idlecallbacksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-ImageManager (0.76.9):
    - glog
    - RCT-Folly/Fabric
    - React-Core/Default
    - React-debug
    - React-Fabric
    - React-graphics
    - React-rendererdebug
    - React-utils
  - React-jserrorhandler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-debug
    - React-jsi
  - React-jsi (0.76.9):
    - boost
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
  - React-jsiexecutor (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-perflogger
  - React-jsinspector (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - React-featureflags
    - React-jsi
    - React-perflogger
    - React-runtimeexecutor
  - React-jsitracing (0.76.9):
    - React-jsi
  - React-logger (0.76.9):
    - glog
  - React-Mapbuffer (0.76.9):
    - glog
    - React-debug
  - React-microtasksnativemodule (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context (4.12.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-safe-area-context/common (= 4.12.0)
    - react-native-safe-area-context/fabric (= 4.12.0)
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/common (4.12.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-safe-area-context/fabric (4.12.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - react-native-safe-area-context/common
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - react-native-webview (13.12.5):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - React-nativeconfig (0.76.9)
  - React-NativeModulesApple (0.76.9):
    - glog
    - hermes-engine
    - React-callinvoker
    - React-Core
    - React-cxxreact
    - React-jsi
    - React-jsinspector
    - React-runtimeexecutor
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - React-perflogger (0.76.9):
    - DoubleConversion
    - RCT-Folly (= 2024.10.14.00)
  - React-performancetimeline (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-cxxreact
    - React-timing
  - React-RCTActionSheet (0.76.9):
    - React-Core/RCTActionSheetHeaders (= 0.76.9)
  - React-RCTAnimation (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTAnimationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTAppDelegate (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-CoreModules
    - React-debug
    - React-defaultsnativemodule
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-hermes
    - React-nativeconfig
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-RCTNetwork
    - React-rendererdebug
    - React-RuntimeApple
    - React-RuntimeCore
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
    - ReactCodegen
    - ReactCommon
  - React-RCTBlob (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTBlobHeaders
    - React-Core/RCTWebSocket
    - React-jsi
    - React-jsinspector
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTFabric (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricComponents
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-jsi
    - React-jsinspector
    - React-nativeconfig
    - React-performancetimeline
    - React-RCTImage
    - React-RCTText
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimescheduler
    - React-utils
    - Yoga
  - React-RCTImage (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTImageHeaders
    - React-jsi
    - React-NativeModulesApple
    - React-RCTNetwork
    - ReactCodegen
    - ReactCommon
  - React-RCTLinking (0.76.9):
    - React-Core/RCTLinkingHeaders (= 0.76.9)
    - React-jsi (= 0.76.9)
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
    - ReactCommon/turbomodule/core (= 0.76.9)
  - React-RCTNetwork (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTNetworkHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTSettings (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - RCTTypeSafety
    - React-Core/RCTSettingsHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-RCTText (0.76.9):
    - React-Core/RCTTextHeaders (= 0.76.9)
    - Yoga
  - React-RCTVibration (0.76.9):
    - RCT-Folly (= 2024.10.14.00)
    - React-Core/RCTVibrationHeaders
    - React-jsi
    - React-NativeModulesApple
    - ReactCodegen
    - ReactCommon
  - React-rendererconsistency (0.76.9)
  - React-rendererdebug (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - RCT-Folly
    - React-debug
  - React-rncore (0.76.9)
  - React-RuntimeApple (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-callinvoker
    - React-Core/Default
    - React-CoreModules
    - React-cxxreact
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-Mapbuffer
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RuntimeCore
    - React-runtimeexecutor
    - React-RuntimeHermes
    - React-runtimescheduler
    - React-utils
  - React-RuntimeCore (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-cxxreact
    - React-featureflags
    - React-jserrorhandler
    - React-jsi
    - React-jsiexecutor
    - React-jsinspector
    - React-performancetimeline
    - React-runtimeexecutor
    - React-runtimescheduler
    - React-utils
  - React-runtimeexecutor (0.76.9):
    - React-jsi (= 0.76.9)
  - React-RuntimeHermes (0.76.9):
    - hermes-engine
    - RCT-Folly/Fabric (= 2024.10.14.00)
    - React-featureflags
    - React-hermes
    - React-jsi
    - React-jsinspector
    - React-jsitracing
    - React-nativeconfig
    - React-RuntimeCore
    - React-utils
  - React-runtimescheduler (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-callinvoker
    - React-cxxreact
    - React-debug
    - React-featureflags
    - React-jsi
    - React-performancetimeline
    - React-rendererconsistency
    - React-rendererdebug
    - React-runtimeexecutor
    - React-timing
    - React-utils
  - React-timing (0.76.9)
  - React-utils (0.76.9):
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - React-debug
    - React-jsi (= 0.76.9)
  - ReactCodegen (0.76.9):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-FabricImage
    - React-featureflags
    - React-graphics
    - React-jsi
    - React-jsiexecutor
    - React-NativeModulesApple
    - React-rendererdebug
    - React-utils
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
  - ReactCommon (0.76.9):
    - ReactCommon/turbomodule (= 0.76.9)
  - ReactCommon/turbomodule (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi
    - React-logger
    - React-perflogger
    - ReactCommon/turbomodule/bridging (= 0.76.9)
    - ReactCommon/turbomodule/core (= 0.76.9)
  - ReactCommon/turbomodule/bridging (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-jsi (= 0.76.9)
    - React-logger
    - React-perflogger
  - ReactCommon/turbomodule/core (0.76.9):
    - DoubleConversion
    - fast_float
    - fmt
    - glog
    - hermes-engine
    - RCT-Folly
    - React-callinvoker
    - React-cxxreact
    - React-debug (= 0.76.9)
    - React-featureflags (= 0.76.9)
    - React-jsi
    - React-logger
    - React-perflogger
    - React-utils (= 0.76.9)
  - RNCAsyncStorage (1.21.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNDateTimePicker (8.3.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNGestureHandler (2.20.2):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated (= 3.16.7)
    - RNReanimated/worklets (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNReanimated/reanimated/apple (= 3.16.7)
    - Yoga
  - RNReanimated/reanimated/apple (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNReanimated/worklets (3.16.7):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - RNScreens (4.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - RNScreens/common (= 4.4.0)
    - Yoga
  - RNScreens/common (4.4.0):
    - DoubleConversion
    - glog
    - hermes-engine
    - RCT-Folly (= 2024.10.14.00)
    - RCTRequired
    - RCTTypeSafety
    - React-Core
    - React-debug
    - React-Fabric
    - React-featureflags
    - React-graphics
    - React-ImageManager
    - React-NativeModulesApple
    - React-RCTFabric
    - React-RCTImage
    - React-rendererdebug
    - React-utils
    - ReactCodegen
    - ReactCommon/turbomodule/bridging
    - ReactCommon/turbomodule/core
    - Yoga
  - SocketRocket (0.7.1)
  - Yoga (0.0.0)

DEPENDENCIES:
  - boost (from `../node_modules/react-native/third-party-podspecs/boost.podspec`)
  - DoubleConversion (from `../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec`)
  - EXConstants (from `../node_modules/expo-constants/ios`)
  - EXImageLoader (from `../node_modules/expo-image-loader/ios`)
  - EXJSONUtils (from `../node_modules/expo-json-utils/ios`)
  - EXManifests (from `../node_modules/expo-manifests/ios`)
  - Expo (from `../node_modules/expo`)
  - expo-dev-client (from `../node_modules/expo-dev-client/ios`)
  - expo-dev-launcher (from `../node_modules/expo-dev-launcher`)
  - expo-dev-menu (from `../node_modules/expo-dev-menu`)
  - expo-dev-menu-interface (from `../node_modules/expo-dev-menu-interface/ios`)
  - ExpoAsset (from `../node_modules/expo-asset/ios`)
  - ExpoBlur (from `../node_modules/expo-blur/ios`)
  - ExpoClipboard (from `../node_modules/expo-clipboard/ios`)
  - ExpoFileSystem (from `../node_modules/expo-file-system/ios`)
  - ExpoFont (from `../node_modules/expo-font/ios`)
  - ExpoHaptics (from `../node_modules/expo-haptics/ios`)
  - ExpoHead (from `../node_modules/expo-router/ios`)
  - ExpoImagePicker (from `../node_modules/expo-image-picker/ios`)
  - ExpoKeepAwake (from `../node_modules/expo-keep-awake/ios`)
  - ExpoLinearGradient (from `../node_modules/expo-linear-gradient/ios`)
  - ExpoLinking (from `../node_modules/expo-linking/ios`)
  - ExpoLocation (from `../node_modules/expo-location/ios`)
  - ExpoModulesCore (from `../node_modules/expo-modules-core`)
  - ExpoSplashScreen (from `../node_modules/expo-splash-screen/ios`)
  - ExpoSymbols (from `../node_modules/expo-symbols/ios`)
  - ExpoSystemUI (from `../node_modules/expo-system-ui/ios`)
  - ExpoWebBrowser (from `../node_modules/expo-web-browser/ios`)
  - EXUpdatesInterface (from `../node_modules/expo-updates-interface/ios`)
  - fast_float (from `../node_modules/react-native/third-party-podspecs/fast_float.podspec`)
  - FBLazyVector (from `../node_modules/react-native/Libraries/FBLazyVector`)
  - fmt (from `../node_modules/react-native/third-party-podspecs/fmt.podspec`)
  - glog (from `../node_modules/react-native/third-party-podspecs/glog.podspec`)
  - hermes-engine (from `../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec`)
  - "maplibre-react-native (from `../node_modules/@maplibre/maplibre-react-native`)"
  - RCT-Folly (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCT-Folly/Fabric (from `../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec`)
  - RCTDeprecation (from `../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation`)
  - RCTRequired (from `../node_modules/react-native/Libraries/Required`)
  - RCTTypeSafety (from `../node_modules/react-native/Libraries/TypeSafety`)
  - React (from `../node_modules/react-native/`)
  - React-callinvoker (from `../node_modules/react-native/ReactCommon/callinvoker`)
  - React-Core (from `../node_modules/react-native/`)
  - React-Core/RCTWebSocket (from `../node_modules/react-native/`)
  - React-CoreModules (from `../node_modules/react-native/React/CoreModules`)
  - React-cxxreact (from `../node_modules/react-native/ReactCommon/cxxreact`)
  - React-debug (from `../node_modules/react-native/ReactCommon/react/debug`)
  - React-defaultsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/defaults`)
  - React-domnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/dom`)
  - React-Fabric (from `../node_modules/react-native/ReactCommon`)
  - React-FabricComponents (from `../node_modules/react-native/ReactCommon`)
  - React-FabricImage (from `../node_modules/react-native/ReactCommon`)
  - React-featureflags (from `../node_modules/react-native/ReactCommon/react/featureflags`)
  - React-featureflagsnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/featureflags`)
  - React-graphics (from `../node_modules/react-native/ReactCommon/react/renderer/graphics`)
  - React-hermes (from `../node_modules/react-native/ReactCommon/hermes`)
  - React-idlecallbacksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks`)
  - React-ImageManager (from `../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios`)
  - React-jserrorhandler (from `../node_modules/react-native/ReactCommon/jserrorhandler`)
  - React-jsi (from `../node_modules/react-native/ReactCommon/jsi`)
  - React-jsiexecutor (from `../node_modules/react-native/ReactCommon/jsiexecutor`)
  - React-jsinspector (from `../node_modules/react-native/ReactCommon/jsinspector-modern`)
  - React-jsitracing (from `../node_modules/react-native/ReactCommon/hermes/executor/`)
  - React-logger (from `../node_modules/react-native/ReactCommon/logger`)
  - React-Mapbuffer (from `../node_modules/react-native/ReactCommon`)
  - React-microtasksnativemodule (from `../node_modules/react-native/ReactCommon/react/nativemodule/microtasks`)
  - react-native-safe-area-context (from `../node_modules/react-native-safe-area-context`)
  - react-native-webview (from `../node_modules/react-native-webview`)
  - React-nativeconfig (from `../node_modules/react-native/ReactCommon`)
  - React-NativeModulesApple (from `../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios`)
  - React-perflogger (from `../node_modules/react-native/ReactCommon/reactperflogger`)
  - React-performancetimeline (from `../node_modules/react-native/ReactCommon/react/performance/timeline`)
  - React-RCTActionSheet (from `../node_modules/react-native/Libraries/ActionSheetIOS`)
  - React-RCTAnimation (from `../node_modules/react-native/Libraries/NativeAnimation`)
  - React-RCTAppDelegate (from `../node_modules/react-native/Libraries/AppDelegate`)
  - React-RCTBlob (from `../node_modules/react-native/Libraries/Blob`)
  - React-RCTFabric (from `../node_modules/react-native/React`)
  - React-RCTImage (from `../node_modules/react-native/Libraries/Image`)
  - React-RCTLinking (from `../node_modules/react-native/Libraries/LinkingIOS`)
  - React-RCTNetwork (from `../node_modules/react-native/Libraries/Network`)
  - React-RCTSettings (from `../node_modules/react-native/Libraries/Settings`)
  - React-RCTText (from `../node_modules/react-native/Libraries/Text`)
  - React-RCTVibration (from `../node_modules/react-native/Libraries/Vibration`)
  - React-rendererconsistency (from `../node_modules/react-native/ReactCommon/react/renderer/consistency`)
  - React-rendererdebug (from `../node_modules/react-native/ReactCommon/react/renderer/debug`)
  - React-rncore (from `../node_modules/react-native/ReactCommon`)
  - React-RuntimeApple (from `../node_modules/react-native/ReactCommon/react/runtime/platform/ios`)
  - React-RuntimeCore (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimeexecutor (from `../node_modules/react-native/ReactCommon/runtimeexecutor`)
  - React-RuntimeHermes (from `../node_modules/react-native/ReactCommon/react/runtime`)
  - React-runtimescheduler (from `../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler`)
  - React-timing (from `../node_modules/react-native/ReactCommon/react/timing`)
  - React-utils (from `../node_modules/react-native/ReactCommon/react/utils`)
  - ReactCodegen (from `build/generated/ios`)
  - ReactCommon/turbomodule/core (from `../node_modules/react-native/ReactCommon`)
  - "RNCAsyncStorage (from `../node_modules/@react-native-async-storage/async-storage`)"
  - "RNDateTimePicker (from `../node_modules/@react-native-community/datetimepicker`)"
  - RNGestureHandler (from `../node_modules/react-native-gesture-handler`)
  - RNReanimated (from `../node_modules/react-native-reanimated`)
  - RNScreens (from `../node_modules/react-native-screens`)
  - Yoga (from `../node_modules/react-native/ReactCommon/yoga`)

SPEC REPOS:
  trunk:
    - SocketRocket

EXTERNAL SOURCES:
  boost:
    :podspec: "../node_modules/react-native/third-party-podspecs/boost.podspec"
  DoubleConversion:
    :podspec: "../node_modules/react-native/third-party-podspecs/DoubleConversion.podspec"
  EXConstants:
    :path: "../node_modules/expo-constants/ios"
  EXImageLoader:
    :path: "../node_modules/expo-image-loader/ios"
  EXJSONUtils:
    :path: "../node_modules/expo-json-utils/ios"
  EXManifests:
    :path: "../node_modules/expo-manifests/ios"
  Expo:
    :path: "../node_modules/expo"
  expo-dev-client:
    :path: "../node_modules/expo-dev-client/ios"
  expo-dev-launcher:
    :path: "../node_modules/expo-dev-launcher"
  expo-dev-menu:
    :path: "../node_modules/expo-dev-menu"
  expo-dev-menu-interface:
    :path: "../node_modules/expo-dev-menu-interface/ios"
  ExpoAsset:
    :path: "../node_modules/expo-asset/ios"
  ExpoBlur:
    :path: "../node_modules/expo-blur/ios"
  ExpoClipboard:
    :path: "../node_modules/expo-clipboard/ios"
  ExpoFileSystem:
    :path: "../node_modules/expo-file-system/ios"
  ExpoFont:
    :path: "../node_modules/expo-font/ios"
  ExpoHaptics:
    :path: "../node_modules/expo-haptics/ios"
  ExpoHead:
    :path: "../node_modules/expo-router/ios"
  ExpoImagePicker:
    :path: "../node_modules/expo-image-picker/ios"
  ExpoKeepAwake:
    :path: "../node_modules/expo-keep-awake/ios"
  ExpoLinearGradient:
    :path: "../node_modules/expo-linear-gradient/ios"
  ExpoLinking:
    :path: "../node_modules/expo-linking/ios"
  ExpoLocation:
    :path: "../node_modules/expo-location/ios"
  ExpoModulesCore:
    :path: "../node_modules/expo-modules-core"
  ExpoSplashScreen:
    :path: "../node_modules/expo-splash-screen/ios"
  ExpoSymbols:
    :path: "../node_modules/expo-symbols/ios"
  ExpoSystemUI:
    :path: "../node_modules/expo-system-ui/ios"
  ExpoWebBrowser:
    :path: "../node_modules/expo-web-browser/ios"
  EXUpdatesInterface:
    :path: "../node_modules/expo-updates-interface/ios"
  fast_float:
    :podspec: "../node_modules/react-native/third-party-podspecs/fast_float.podspec"
  FBLazyVector:
    :path: "../node_modules/react-native/Libraries/FBLazyVector"
  fmt:
    :podspec: "../node_modules/react-native/third-party-podspecs/fmt.podspec"
  glog:
    :podspec: "../node_modules/react-native/third-party-podspecs/glog.podspec"
  hermes-engine:
    :podspec: "../node_modules/react-native/sdks/hermes-engine/hermes-engine.podspec"
    :tag: hermes-2024-11-12-RNv0.76.2-5b4aa20c719830dcf5684832b89a6edb95ac3d64
  maplibre-react-native:
    :path: "../node_modules/@maplibre/maplibre-react-native"
  RCT-Folly:
    :podspec: "../node_modules/react-native/third-party-podspecs/RCT-Folly.podspec"
  RCTDeprecation:
    :path: "../node_modules/react-native/ReactApple/Libraries/RCTFoundation/RCTDeprecation"
  RCTRequired:
    :path: "../node_modules/react-native/Libraries/Required"
  RCTTypeSafety:
    :path: "../node_modules/react-native/Libraries/TypeSafety"
  React:
    :path: "../node_modules/react-native/"
  React-callinvoker:
    :path: "../node_modules/react-native/ReactCommon/callinvoker"
  React-Core:
    :path: "../node_modules/react-native/"
  React-CoreModules:
    :path: "../node_modules/react-native/React/CoreModules"
  React-cxxreact:
    :path: "../node_modules/react-native/ReactCommon/cxxreact"
  React-debug:
    :path: "../node_modules/react-native/ReactCommon/react/debug"
  React-defaultsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/defaults"
  React-domnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/dom"
  React-Fabric:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricComponents:
    :path: "../node_modules/react-native/ReactCommon"
  React-FabricImage:
    :path: "../node_modules/react-native/ReactCommon"
  React-featureflags:
    :path: "../node_modules/react-native/ReactCommon/react/featureflags"
  React-featureflagsnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/featureflags"
  React-graphics:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/graphics"
  React-hermes:
    :path: "../node_modules/react-native/ReactCommon/hermes"
  React-idlecallbacksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/idlecallbacks"
  React-ImageManager:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/imagemanager/platform/ios"
  React-jserrorhandler:
    :path: "../node_modules/react-native/ReactCommon/jserrorhandler"
  React-jsi:
    :path: "../node_modules/react-native/ReactCommon/jsi"
  React-jsiexecutor:
    :path: "../node_modules/react-native/ReactCommon/jsiexecutor"
  React-jsinspector:
    :path: "../node_modules/react-native/ReactCommon/jsinspector-modern"
  React-jsitracing:
    :path: "../node_modules/react-native/ReactCommon/hermes/executor/"
  React-logger:
    :path: "../node_modules/react-native/ReactCommon/logger"
  React-Mapbuffer:
    :path: "../node_modules/react-native/ReactCommon"
  React-microtasksnativemodule:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/microtasks"
  react-native-safe-area-context:
    :path: "../node_modules/react-native-safe-area-context"
  react-native-webview:
    :path: "../node_modules/react-native-webview"
  React-nativeconfig:
    :path: "../node_modules/react-native/ReactCommon"
  React-NativeModulesApple:
    :path: "../node_modules/react-native/ReactCommon/react/nativemodule/core/platform/ios"
  React-perflogger:
    :path: "../node_modules/react-native/ReactCommon/reactperflogger"
  React-performancetimeline:
    :path: "../node_modules/react-native/ReactCommon/react/performance/timeline"
  React-RCTActionSheet:
    :path: "../node_modules/react-native/Libraries/ActionSheetIOS"
  React-RCTAnimation:
    :path: "../node_modules/react-native/Libraries/NativeAnimation"
  React-RCTAppDelegate:
    :path: "../node_modules/react-native/Libraries/AppDelegate"
  React-RCTBlob:
    :path: "../node_modules/react-native/Libraries/Blob"
  React-RCTFabric:
    :path: "../node_modules/react-native/React"
  React-RCTImage:
    :path: "../node_modules/react-native/Libraries/Image"
  React-RCTLinking:
    :path: "../node_modules/react-native/Libraries/LinkingIOS"
  React-RCTNetwork:
    :path: "../node_modules/react-native/Libraries/Network"
  React-RCTSettings:
    :path: "../node_modules/react-native/Libraries/Settings"
  React-RCTText:
    :path: "../node_modules/react-native/Libraries/Text"
  React-RCTVibration:
    :path: "../node_modules/react-native/Libraries/Vibration"
  React-rendererconsistency:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/consistency"
  React-rendererdebug:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/debug"
  React-rncore:
    :path: "../node_modules/react-native/ReactCommon"
  React-RuntimeApple:
    :path: "../node_modules/react-native/ReactCommon/react/runtime/platform/ios"
  React-RuntimeCore:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimeexecutor:
    :path: "../node_modules/react-native/ReactCommon/runtimeexecutor"
  React-RuntimeHermes:
    :path: "../node_modules/react-native/ReactCommon/react/runtime"
  React-runtimescheduler:
    :path: "../node_modules/react-native/ReactCommon/react/renderer/runtimescheduler"
  React-timing:
    :path: "../node_modules/react-native/ReactCommon/react/timing"
  React-utils:
    :path: "../node_modules/react-native/ReactCommon/react/utils"
  ReactCodegen:
    :path: build/generated/ios
  ReactCommon:
    :path: "../node_modules/react-native/ReactCommon"
  RNCAsyncStorage:
    :path: "../node_modules/@react-native-async-storage/async-storage"
  RNDateTimePicker:
    :path: "../node_modules/@react-native-community/datetimepicker"
  RNGestureHandler:
    :path: "../node_modules/react-native-gesture-handler"
  RNReanimated:
    :path: "../node_modules/react-native-reanimated"
  RNScreens:
    :path: "../node_modules/react-native-screens"
  Yoga:
    :path: "../node_modules/react-native/ReactCommon/yoga"

SPEC CHECKSUMS:
  boost: 1dca942403ed9342f98334bf4c3621f011aa7946
  DoubleConversion: f16ae600a246532c4020132d54af21d0ddb2a385
  EXConstants: fcfc75800824ac2d5c592b5bc74130bad17b146b
  EXImageLoader: 1fe96c70cdc78bedc985ec4b1fab5dd8e67dc38b
  EXJSONUtils: 01fc7492b66c234e395dcffdd5f53439c5c29c93
  EXManifests: a19d50504b8826546a4782770317bc83fffec87d
  Expo: a6ff273c618506b12129a0d06f2a08201bfbcf43
  expo-dev-client: db44302cdbe0ec55b0ef1849c9a23a76dec6dbac
  expo-dev-launcher: 792cd1c83fbec4a1a66fe91a0c283368dbad851c
  expo-dev-menu: dd3197d2b0107ee036ffd85f95e75a950ab52ada
  expo-dev-menu-interface: 00dc42302a72722fdecec3fa048de84a9133bcc4
  ExpoAsset: 48386d40d53a8c1738929b3ed509bcad595b5516
  ExpoBlur: 392c1207f71d0ecf22371621c1fbd44ba84d9742
  ExpoClipboard: 44fd1c8959ee8f6175d059dc011b154c9709a969
  ExpoFileSystem: 42d363d3b96f9afab980dcef60d5657a4443c655
  ExpoFont: f354e926f8feae5e831ec8087f36652b44a0b188
  ExpoHaptics: 8d199b2f33245ea85289ff6c954c7ee7c00a5b5d
  ExpoHead: cee2d16ef197aaadb0ac481cf221a663636eb074
  ExpoImagePicker: 517a47896adf5d55d0a1c159e5d1e312af12e57c
  ExpoKeepAwake: b0171a73665bfcefcfcc311742a72a956e6aa680
  ExpoLinearGradient: 35ebd83b16f80b3add053a2fd68cc328ed927f60
  ExpoLinking: 8d12bee174ba0cdf31239706578e29e74a417402
  ExpoLocation: de58a60756fad907139ec422ccabb1ba734e080d
  ExpoModulesCore: c25d77625038b1968ea1afefc719862c0d8dd993
  ExpoSplashScreen: 8261985ce9778f904abc7e31bed3538dce67ed4d
  ExpoSymbols: f3002db15156cd4e505c77b6ea1df5c984db9965
  ExpoSystemUI: b82a45cf0f6a4fa18d07c46deba8725dd27688b4
  ExpoWebBrowser: a212e6b480d8857d3e441fba51e0c968333803b3
  EXUpdatesInterface: 7c977640bdd8b85833c19e3959ba46145c5719db
  fast_float: 06eeec4fe712a76acc9376682e4808b05ce978b6
  FBLazyVector: 7605ea4810e0e10ae4815292433c09bf4324ba45
  fmt: 01b82d4ca6470831d1cc0852a1af644be019e8f6
  glog: 08b301085f15bcbb6ff8632a8ebaf239aae04e6a
  hermes-engine: 9e868dc7be781364296d6ee2f56d0c1a9ef0bb11
  maplibre-react-native: 06a2ef4611809e49cf5406766b1f6594e8b835e0
  RCT-Folly: ea9d9256ba7f9322ef911169a9f696e5857b9e17
  RCTDeprecation: ebe712bb05077934b16c6bf25228bdec34b64f83
  RCTRequired: ca91e5dd26b64f577b528044c962baf171c6b716
  RCTTypeSafety: e7678bd60850ca5a41df9b8dc7154638cb66871f
  React: 4641770499c39f45d4e7cde1eba30e081f9d8a3d
  React-callinvoker: 4bef67b5c7f3f68db5929ab6a4d44b8a002998ea
  React-Core: a68cea3e762814e60ecc3fa521c7f14c36c99245
  React-CoreModules: d81b1eaf8066add66299bab9d23c9f00c9484c7c
  React-cxxreact: 984f8b1feeca37181d4e95301fcd6f5f6501c6ab
  React-debug: 817160c07dc8d24d020fbd1eac7b3558ffc08964
  React-defaultsnativemodule: 18a684542f82ce1897552a1c4b847be414c9566e
  React-domnativemodule: 90bdd4ec3ab38c47cfc3461c1e9283a8507d613f
  React-Fabric: f6dade7007533daeb785ba5925039d83f343be4b
  React-FabricComponents: b0655cc3e1b5ae12a4a1119aa7d8308f0ad33520
  React-FabricImage: 9b157c4c01ac2bf433f834f0e1e5fe234113a576
  React-featureflags: f2792b067a351d86fdc7bec23db3b9a2f2c8d26c
  React-featureflagsnativemodule: 742a8325b3c821d2a1ca13a6d2a0fc72d04555e0
  React-graphics: 68969e4e49d73f89da7abef4116c9b5f466aa121
  React-hermes: ac0bcba26a5d288ebc99b500e1097da2d0297ddf
  React-idlecallbacksnativemodule: d61d9c9816131bf70d3d80cd04889fc625ee523f
  React-ImageManager: e906eec93a9eb6102a06576b89d48d80a4683020
  React-jserrorhandler: ac5dde01104ff444e043cad8f574ca02756e20d6
  React-jsi: 496fa2b9d63b726aeb07d0ac800064617d71211d
  React-jsiexecutor: dd22ab48371b80f37a0a30d0e8915b6d0f43a893
  React-jsinspector: 4629ac376f5765e684d19064f2093e55c97fd086
  React-jsitracing: 7a1c9cd484248870cf660733cd3b8114d54c035f
  React-logger: c4052eb941cca9a097ef01b59543a656dc088559
  React-Mapbuffer: 33546a3ebefbccb8770c33a1f8a5554fa96a54de
  React-microtasksnativemodule: d80ff86c8902872d397d9622f1a97aadcc12cead
  react-native-safe-area-context: cd916088cac5300c3266876218377518987b995e
  react-native-webview: 6b9fc65c1951203a3e958ff3cc0a858d4b6be901
  React-nativeconfig: 8efdb1ef1e9158c77098a93085438f7e7b463678
  React-NativeModulesApple: cebca2e5320a3d66e123cade23bd90a167ffce5e
  React-perflogger: 72e653eb3aba9122f9e57cf012d22d2486f33358
  React-performancetimeline: cd6a9374a72001165995d2ab632f672df04076dc
  React-RCTActionSheet: aacf2375084dea6e7c221f4a727e579f732ff342
  React-RCTAnimation: 395ab53fd064dff81507c15efb781c8684d9a585
  React-RCTAppDelegate: 345a6f1b82abc578437df0ce7e9c48740eca827c
  React-RCTBlob: 13311e554c1a367de063c10ee7c5e6573b2dd1d6
  React-RCTFabric: 007b1a98201cc49b5bc6e1417d7fe3f6fc6e2b78
  React-RCTImage: 1b1f914bcc12187c49ba5d949dac38c2eb9f5cc8
  React-RCTLinking: 4ac7c42beb65e36fba0376f3498f3cd8dd0be7fa
  React-RCTNetwork: 938902773add4381e84426a7aa17a2414f5f94f7
  React-RCTSettings: e848f1ba17a7a18479cf5a31d28145f567da8223
  React-RCTText: 7e98fafdde7d29e888b80f0b35544e0cb07913cf
  React-RCTVibration: cd7d80affd97dc7afa62f9acd491419558b64b78
  React-rendererconsistency: b4917053ecbaa91469c67a4319701c9dc0d40be6
  React-rendererdebug: aa181c36dd6cf5b35511d1ed875d6638fd38f0ec
  React-rncore: 120d21715c9b4ba8f798bffe986cb769b988dd74
  React-RuntimeApple: d033becbbd1eba6f9f6e3af6f1893030ce203edd
  React-RuntimeCore: 38af280bb678e66ba000a3c3d42920b2a138eebb
  React-runtimeexecutor: 877596f82f5632d073e121cba2d2084b76a76899
  React-RuntimeHermes: 37aad735ff21ca6de2d8450a96de1afe9f86c385
  React-runtimescheduler: 8ec34cc885281a34696ea16c4fd86892d631f38d
  React-timing: 331cbf9f2668c67faddfd2e46bb7f41cbd9320b9
  React-utils: ed818f19ab445000d6b5c4efa9d462449326cc9f
  ReactCodegen: f853a20cc9125c5521c8766b4b49375fec20648b
  ReactCommon: 300d8d9c5cb1a6cd79a67cf5d8f91e4d477195f9
  RNCAsyncStorage: 52c9415cd84ef457827319c862c89412e1b0e362
  RNDateTimePicker: 96559f666636a8326e862024103eab77a6950625
  RNGestureHandler: fffddeb8af59709c6d8de11b6461a6af63cad532
  RNReanimated: 2e5069649cbab2c946652d3b97589b2ae0526220
  RNScreens: 362f4c861dd155f898908d5035d97b07a3f1a9d1
  SocketRocket: d4aabe649be1e368d1318fdf28a022d714d65748
  Yoga: feb4910aba9742cfedc059e2b2902e22ffe9954a

PODFILE CHECKSUM: 427d53da246cb0312d0fb2facc31ccc396d8fc45

COCOAPODS: 1.16.2
