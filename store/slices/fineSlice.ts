import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { checkTrafficViolations } from "@/api/traffic-violations.api";

// Định nghĩa kiểu dữ liệu cho nơi giải quyết vi phạm

// Định nghĩa kiểu dữ liệu cho vi phạm
export interface Violation {
  violationTime: string;
  location: string;
  violationType: string;
  status: string;
  detectionUnit: string;
  name: string;
  address: string;
  phone: string;
}

// Định nghĩa kiểu dữ liệu cho SearchResult
export interface SearchResult {
  licensePlate: string;
  htmlContent?: string; // Giữ lại để tương thích ngược
  totalViolations: number;
  pendingViolations: number;
  resolvedViolations: number;
  violations: Violation[];
  plateColor: string;
  vehicleType: string;
}

// Định nghĩa kiểu dữ liệu cho state
export interface FineState {
  searchResult: SearchResult | null;
  isLoading: boolean;
  error: string | null;
  searchHistory: string[]; // <PERSON><PERSON><PERSON> lịch sử các biển số đã tra cứu
}

// Khởi tạo giá trị ban đầu cho state
const initialState: FineState = {
  searchResult: null,
  isLoading: false,
  error: null,
  searchHistory: [],
};

// Định nghĩa kiểu dữ liệu cho response từ API
export interface ApiResponse {
  success: boolean;
  message: string;
  totalViolations: number;
  pendingViolations: number;
  resolvedViolations: number;
  violations: Violation[];
  licensePlate: string;
  plateColor: string;
  vehicleType: string;
}

// Tạo async thunk để xử lý việc tra cứu phạt nguội
export const searchFines = createAsyncThunk(
  "fine/searchFines",
  async (licensePlate: string, { rejectWithValue }) => {
    try {
      const responseData = await checkTrafficViolations(licensePlate);

      // Kiểm tra success flag
      if (!responseData.success) {
        return rejectWithValue(
          responseData.message || "Có lỗi xảy ra khi tra cứu phạt nguội"
        );
      }

      // Trả về kết quả với cấu trúc mới
      return {
        licensePlate: responseData.licensePlate || licensePlate.toUpperCase(), // Sử dụng biển số từ API nếu có, nếu không thì dùng biển số đầu vào
        totalViolations: responseData.totalViolations,
        pendingViolations: responseData.pendingViolations,
        resolvedViolations: responseData.resolvedViolations,
        violations: responseData.violations,
        plateColor: responseData.plateColor || '',
        vehicleType: responseData.vehicleType || '',
      } as SearchResult;
    } catch (error) {
      console.error("Error searching for traffic violations:", error);
      return rejectWithValue(
        "Có lỗi xảy ra khi kết nối đến máy chủ. Vui lòng thử lại sau."
      );
    }
  }
);

// Tạo slice
const fineSlice = createSlice({
  name: "fine",
  initialState,
  reducers: {
    clearSearchResult: (state) => {
      state.searchResult = null;
      state.error = null;
    },
    clearSearchHistory: (state) => {
      state.searchHistory = [];
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(searchFines.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(
        searchFines.fulfilled,
        (state, action: PayloadAction<SearchResult>) => {
          state.isLoading = false;
          state.searchResult = action.payload;

          // Thêm biển số vào lịch sử tra cứu nếu chưa có
          if (!state.searchHistory.includes(action.payload.licensePlate)) {
            state.searchHistory.unshift(action.payload.licensePlate);

            // Giới hạn lịch sử tra cứu tối đa 10 biển số
            if (state.searchHistory.length > 10) {
              state.searchHistory = state.searchHistory.slice(0, 10);
            }
          }
        }
      )
      .addCase(searchFines.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions
export const { clearSearchResult, clearSearchHistory } = fineSlice.actions;

// Export reducer
export default fineSlice.reducer;
