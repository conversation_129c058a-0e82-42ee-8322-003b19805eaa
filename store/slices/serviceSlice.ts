import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Đ<PERSON>nh nghĩa kiểu dữ liệu cho service state
export interface ServiceState {
  note: string;
  promoCode: string;
}

// Khởi tạo giá trị ban đầu cho state
const initialState: ServiceState = {
  note: '',
  promoCode: '',
};

// Tạo slice
export const serviceSlice = createSlice({
  name: 'service',
  initialState,
  reducers: {
    // Action để cập nhật ghi chú
    updateNote: (state, action: PayloadAction<string>) => {
      state.note = action.payload;
    },
    // Action để cập nhật mã quảng cáo
    updatePromoCode: (state, action: PayloadAction<string>) => {
      state.promoCode = action.payload;
    },
    // Action để xóa ghi chú
    clearNote: (state) => {
      state.note = '';
    },
    // Action để xóa mã quảng cáo
    clearPromoCode: (state) => {
      state.promoCode = '';
    },
    // Action để reset toàn bộ state
    resetService: (state) => {
      return initialState;
    },
  },
});

// Export các action
export const { updateNote, updatePromoCode, clearNote, clearPromoCode, resetService } = serviceSlice.actions;

// Export reducer
export default serviceSlice.reducer;
