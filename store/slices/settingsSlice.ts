import { createSlice, createAsyncThunk, PayloadAction } from "@reduxjs/toolkit";
import { Setting, getAllSettings } from "@/api/settings.api";

// Define the shape of the settings state
interface SettingsState {
  settings: Setting[];
  settingsMap: Record<string, any>;
  loading: boolean;
  error: string | null;
}

// Initial state
const initialState: SettingsState = {
  settings: [],
  settingsMap: {},
  loading: false,
  error: null,
};

// Async thunk to fetch all settings
export const fetchAllSettings = createAsyncThunk(
  "settings/fetchAll",
  async (_, { rejectWithValue }) => {
    try {
      return await getAllSettings();
    } catch (error) {
      return rejectWithValue(
        (error instanceof Error && (error as any).response?.data?.message) || "Failed to fetch settings"
      );
    }
  }
);

// Create the settings slice
const settingsSlice = createSlice({
  name: "settings",
  initialState,
  reducers: {
    // Reset settings state
    resetSettings: () => initialState,

    // Update a specific setting in the map
    updateSetting: (
      state,
      action: PayloadAction<{ key: string; value: any }>
    ) => {
      const { key, value } = action.payload;
      state.settingsMap[key] = value;
    },

    // Set default settings for testing
    setDefaultSettings: (state) => {
      state.settings = [
        {
          key: "saygo_local_service_fee",
          value: { value: 100000 }
        },
        {
          key: "saygo_local_distance_fee_first_10km",
          value: { value: 16000 }
        },
        {
          key: "saygo_local_distance_fee_11_to_20km",
          value: { value: 12000 }
        },
        {
          key: "saygo_local_distance_fee_after_20km",
          value: { value: 8000 }
        },
        {
          key: "saygo_local_waiting_fee",
          value: { value: 50000 }
        },
        {
          key: "saygo_local_night_surcharge_21",
          value: { value: 10 }
        },
        {
          key: "saygo_local_night_surcharge_23",
          value: { value: 20 }
        },
        {
          key: "saygo_local_additional_fees",
          value: { value: "Giá trên chưa bao gồm cước phí cầu đường, phí gửi xe, phí phà, phí vào bến xe nếu có" }
        },
        {
          key: "saygo_local_service_fee_amount",
          value: { value: 30000 }
        }
      ];
    },
  },
  extraReducers: (builder) => {
    // Handle fetchAllSettings
    builder
      .addCase(fetchAllSettings.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchAllSettings.fulfilled, (state, action) => {
        state.loading = false;
        // Handle both API response formats
        if (action.payload.data) {
          // Standard API response with data property
          state.settings = action.payload.data;
        } else if (Array.isArray(action.payload)) {
          // Direct array response
          state.settings = action.payload;
        } else {
          state.settings = [];
        }
      })
      .addCase(fetchAllSettings.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const { resetSettings, updateSetting, setDefaultSettings } = settingsSlice.actions;
export default settingsSlice.reducer;
