import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// Định nghĩa kiểu dữ liệu cho user state
export interface UserState {
  id: string | null;
  name: string | null;
  email: string | null;
  phone: string | null;
  photo: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

// Khởi tạo giá trị ban đầu cho state
const initialState: UserState = {
  id: null,
  name: null,
  email: null,
  phone: null,
  photo: null,
  isAuthenticated: false,
  isLoading: false,
  error: null,
};

// Tạo slice
export const userSlice = createSlice({
  name: 'user',
  initialState,
  reducers: {
    // Action để bắt đầu quá trình đăng nhập
    loginStart: (state) => {
      state.isLoading = true;
      state.error = null;
    },
    // Action khi đăng nhập thành công
    loginSuccess: (state, action: PayloadAction<{ id: string; name: string; email?: string; phone: string; photo?: string }>) => {
      state.isLoading = false;
      state.isAuthenticated = true;
      state.id = action.payload.id;
      state.name = action.payload.name;
      if (action.payload.email) state.email = action.payload.email;
      state.phone = action.payload.phone;
      state.photo = action.payload.photo || null;
      state.error = null;
    },
    // Action khi đăng nhập thất bại
    loginFailure: (state, action: PayloadAction<string>) => {
      state.isLoading = false;
      state.error = action.payload;
    },
    // Action để đăng xuất
    logout: (state) => {
      return initialState;
    },
    // Action để cập nhật thông tin người dùng
    updateUserInfo: (state, action: PayloadAction<{ name?: string; email?: string; phone?: string; photo?: string }>) => {
      if (action.payload.name) state.name = action.payload.name;
      if (action.payload.phone) state.phone = action.payload.phone;
      if (action.payload.photo) state.photo = action.payload.photo;
    },
  },
});

// Export các action
export const { loginStart, loginSuccess, loginFailure, logout, updateUserInfo } = userSlice.actions;

// Export reducer
export default userSlice.reducer;
