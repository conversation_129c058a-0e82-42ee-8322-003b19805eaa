import { StyleSheet, TextStyle } from 'react-native';
import { FONTS } from '@/constants/Typography';

/**
 * Utility function to apply font styles to any component
 * @param styles - The styles to apply the font to
 * @returns The styles with the font applied
 */
export function applyFontToStyles(styles: Record<string, TextStyle>) {
  const newStyles: Record<string, TextStyle> = {};
  
  Object.keys(styles).forEach(key => {
    const style = styles[key];
    newStyles[key] = {
      ...style,
      fontFamily: style.fontStyle === 'italic' ? FONTS.ITALIC : FONTS.REGULAR,
    };
  });
  
  return StyleSheet.create(newStyles);
}
